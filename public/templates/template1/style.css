/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6b1d1d;
    --secondary-color: #2d3748;
    --accent-color: #ff5722;
    --heading-font: 'Playfair Display', serif;
    --body-font: '<PERSON>o', sans-serif;
}

/* Global Styles */
body {
    font-family: var(--body-font);
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header */
header {
    background: var(--primary-color);
    color: white;
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    font-family: var(--heading-font);
}

nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
}

nav a:hover {
    text-decoration: underline;
}

/* Hero Section */
#hero {
  /* Remove static background-image */
  background: center/cover no-repeat;
  /* Add gradient overlay */
  background-image: linear-gradient(rgba(0,0,0,0.5), var(--dynamic-hero-image));
}

[data-image-index] img {
  transition: transform 0.3s ease;
  min-height: 200px;
  object-fit: cover;
}

[data-image-index]:hover img {
  transform: scale(1.05);
}

.hero-content h2 {
    font-size: 48px;
    margin-bottom: 20px;
    font-family: var(--heading-font);
    color: var(--primary-color);
}

.hero-content p {
    font-size: 20px;
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    padding: 12px 25px;
    background: var(--accent-color);
    color: white;
    text-decoration: none;
    border-radius: 4px;
}

.btn:hover {
    background: #e64a19;
}

/* Sections */
.section {
    padding: 80px 20px;
    text-align: center;
}

.section h2 {
    font-size: 36px;
    margin-bottom: 30px;
    font-family: var(--heading-font);
}

/* Menu Grid */
.menu-grid {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
}

.menu-item {
    width: 300px;
}

.menu-item img {
    width: 100%;
    border-radius: 8px;
}

.menu-item h3 {
    margin-top: 10px;
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.gallery-grid img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Contact */
#contact p {
    margin: 10px 0;
}

/* Footer */
footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 20px 0;
}

[data-image-replace] img {
    /* Ensure images maintain aspect ratio */
    height: 200px;
    object-fit: cover;
}

/* Add smooth transitions for dynamic changes */
.menu-item img, #hero {
    transition: all 0.3s ease;
}
