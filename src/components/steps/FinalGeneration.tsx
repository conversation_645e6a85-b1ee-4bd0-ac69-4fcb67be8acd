'use client';

import React from 'react';
import { useSiteBuilder } from 'src/context/SiteBuilderContext';

export const FinalGeneration: React.FC = () => {
  const { formData, prevStep } = useSiteBuilder();

  const {
    selectedSitePath,
    previewKey,
    images,
  } = formData;

  const heroImage = images[0]?.url || '';
  const headerImage = images[1]?.url || '';

  const previewUrl =
    selectedSitePath && previewKey
      ? `${selectedSitePath}/?preview_key=${previewKey}&heroImageUrl=${encodeURIComponent(
          heroImage
        )}&headerImageUrl=${encodeURIComponent(headerImage)}&_refresh=${Date.now()}`
      : '';

  return (
    <div className="fixed inset-0 z-50">
      {/* Back Button */}
      <button
        onClick={prevStep}
        className="absolute top-4 left-4 z-50 bg-white text-black px-4 py-2 rounded shadow"
      >
        Back
      </button>

      {/* Fullscreen iframe preview */}
      <iframe
        src={previewUrl}
        className="w-full h-full border-none"
        title="Final Website Preview"
      />
    </div>
  );
};
