'use client';

import React, { useEffect, useState } from 'react';
import { useSiteBuilder } from '@/context/SiteBuilderContext';

interface TemplatePayload {
  templateId: string;
  name: string;
  description: string;
  thumbnailUrl: string;
  site_path: string;
}

export const TemplateSelection: React.FC = () => {
  const { formData, nextStep, updateFormData, setGlobalLoading } = useSiteBuilder();

  const [templates, setTemplates] = useState<TemplatePayload[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

  const API_BASE = process.env.NEXT_PUBLIC_WORDPRESS_API;

  const loadTemplates = async () => {
    if (!formData.category) return;

    setGlobalLoading(true);
    try {
      const res = await fetch(
        `${API_BASE}/wp-json/custom/v1/templates-by-category?category=${encodeURIComponent(
          formData.category
        )}`
      );
      if (!res.ok) throw new Error('Failed to fetch templates');
      const data = await res.json();
      setTemplates(data);
    } catch (err) {
      console.error('Error loading templates:', err);
    } finally {
      setGlobalLoading(false);
    }
  };

  useEffect(() => {
    loadTemplates();
  }, [formData.category]);

  const handleCardClick = async (template: TemplatePayload, index: number) => {
    setSelectedIndex(index);
    setGlobalLoading(true);

    const previewData = {
      siteName: formData.name,
      brief: formData.description,
      heroImage: formData.selectedImages?.[0] || '',
      headerImage: formData.selectedImages?.[1] || '',
    };

    try {
      const res = await fetch(`${template.site_path}/wp-json/dynamic-theme/v1/preview-data`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(previewData),
      });

      if (!res.ok) throw new Error('Preview data update failed');
      const data = await res.json();

      updateFormData({
        selectedTemplate: index,
        selectedSitePath: template.site_path,
        previewKey: data.transient_key,
      });

      nextStep();
    } catch (err) {
      console.error('Error updating preview data:', err);
      alert('Something went wrong while loading the template. Please try again.');
    } finally {
      setGlobalLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-screen w-full bg-gray-50">
      <div className="flex-1 overflow-y-auto px-4 sm:px-8 md:px-12 lg:px-16 xl:px-20 py-6">
        <h2 className="text-lg sm:text-2xl md:text-3xl font-bold mb-6 text-center">Choose a Homepage Template</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          {templates.map((template, index) => (
            <div
              key={template.templateId}
              onClick={() => handleCardClick(template, index)}
              className={`cursor-pointer w-full bg-white border rounded-xl shadow-sm hover:shadow-md transition overflow-hidden ${
                selectedIndex === index ? 'ring-2 ring-[#7DBE1D]' : ''
              }`}
            >
              <div className="p-4 flex flex-col items-center">
                <img
                  src={template.thumbnailUrl || '/images/dummy-thumbnail.jpg'}
                  alt={`${template.name} Thumbnail`}
                  className="w-full h-[180px] object-contain rounded-md bg-white mb-3"
                />
                <h3 className="text-base font-semibold text-center">{template.name}</h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
