// This component is used to preview a template by rendering it in an iframe.
import React from 'react';

interface TemplatePreviewProps {
  postId: number;
  siteName: string;
  brief: string;
  heroImage: string;
  phone?: string;
  email?: string;
  themeSlug: string; // <-- new prop
  headerImage:string; // <-- new prop
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  postId,
  siteName,
  brief,
  heroImage,
  phone,
  email,
  themeSlug, // <-- new prop
  headerImage, // <-- new prop
}) => {
  const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;
  const url = new URL(BASE_URL);
  url.searchParams.set('id', String(postId));
  url.searchParams.set('website_name', siteName);
  url.searchParams.set('website_subtitle', brief);
  url.searchParams.set('hero_image', heroImage);
  url.searchParams.set('hero_image', headerImage);
  if (phone) url.searchParams.set('phone', phone);
  if (email) url.searchParams.set('email', email);
  if (themeSlug) url.searchParams.set('preview_theme', themeSlug); // 👈 important

  return (
    <iframe
      src={url.toString()}
      style={{ width: '100%', height: '100vh', border: 'none' }}
      title="Template Preview"
    />
  );
};

export default TemplatePreview;


