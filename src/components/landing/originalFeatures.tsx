import React, { useEffect, useRef } from 'react';
import { 
  Brain, 
  Palette, 
  Smartphone, 
  Search, 
  Shield, 
  Zap,
  Code,
  Users,
  TrendingUp
} from 'lucide-react';

const features = [
  {
    icon: Brain,
    title: 'AI-Powered Design',
    description: 'Our advanced AI understands your brand and creates stunning designs that convert visitors into customers.',
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: Palette,
    title: 'Smart Color Schemes',
    description: 'Automatically generate professional color palettes that match your brand identity and industry standards.',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: Smartphone,
    title: 'Mobile-First Responsive',
    description: 'Every site is built mobile-first with perfect responsive design across all devices and screen sizes.',
    color: 'from-emerald-500 to-teal-500'
  },
  {
    icon: Search,
    title: 'SEO Optimization',
    description: 'Built-in SEO best practices ensure your site ranks higher in search results from day one.',
    color: 'from-orange-500 to-red-500'
  },
  {
    icon: Shield,
    title: 'Enterprise Security',
    description: 'Bank-level security features protect your site and visitor data with advanced encryption.',
    color: 'from-indigo-500 to-purple-500'
  },
  {
    icon: Zap,
    title: 'Lightning Performance',
    description: 'Optimized code and smart caching deliver blazing-fast loading speeds for better user experience.',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: Code,
    title: 'No-Code Editor',
    description: 'Intuitive drag-and-drop interface lets you customize everything without writing a single line of code.',
    color: 'from-pink-500 to-rose-500'
  },
  {
    icon: Users,
    title: 'Team Collaboration',
    description: 'Work together with your team in real-time with advanced collaboration and permission features.',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: TrendingUp,
    title: 'Analytics & Insights',
    description: 'Built-in analytics help you understand your visitors and optimize your site for better conversions.',
    color: 'from-blue-500 to-indigo-500'
  }
];

const Features: React.FC = () => {
  const featuresRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
              setTimeout(() => {
                card.classList.add('animate-fade-in-up');
              }, index * 100);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (featuresRef.current) {
      observer.observe(featuresRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={featuresRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
            Powerful Features for{' '}
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Modern Websites
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to create, launch, and grow your WordPress website with the power of artificial intelligence.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="feature-card opacity-0 transform translate-y-8 group bg-white rounded-2xl p-8 shadow-sm hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-gray-200"
            >
              <div className={`inline-flex items-center justify-center w-14 h-14 rounded-xl bg-gradient-to-r ${feature.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="w-7 h-7 text-white" />
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;