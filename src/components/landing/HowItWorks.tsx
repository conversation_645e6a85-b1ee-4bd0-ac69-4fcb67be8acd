// //without responsive design
// import React, { useEffect, useRef } from 'react';
// import { MessageSquare, Wand2, Rocket, CheckCircle } from 'lucide-react';

// const steps = [
//   {
//     icon: MessageSquare,
//     title: 'Describe Your Vision',
//     description: 'Tell our AI what kind of website you want to build. Share your ideas, goals, and preferences in natural language.',
//     color: 'from-[#7DBE1D] to-[#C5DB9D]'
//   },
//   {
//     icon: Wand2,
//     title: 'AI Creates Your Site',
//     description: 'Our intelligent system analyzes your requirements and generates a complete WordPress site with custom design and content.',
//     color: 'from-[#8180A7] to-[#C5DB9D]'
//   },
//   {
//     icon: CheckCircle,
//     title: 'Customize & Perfect',
//     description: 'Fine-tune your site with our intuitive editor. Adjust colors, layouts, content, and features to match your vision perfectly.',
//     color: 'from-[#7DBE1D] to-[#8180A7]'
//   },
//   {
//     icon: Rocket,
//     title: 'Launch & Grow',
//     description: 'Deploy your site with one click and start growing your business. Built-in SEO and performance optimization included.',
//     color: 'from-[#C5DB9D] to-[#8180A7]'
//   }
// ];

// const HowItWorks: React.FC = () => {
//   const sectionRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const observer = new IntersectionObserver(
//       (entries) => {
//         entries.forEach((entry) => {
//           if (entry.isIntersecting) {
//             const steps = entry.target.querySelectorAll('.step-card');
//             steps.forEach((step, index) => {
//               setTimeout(() => {
//                 step.classList.add('animate-fade-in-up');
//               }, index * 200);
//             });
//           }
//         });
//       },
//       { threshold: 0.1 }
//     );

//     if (sectionRef.current) {
//       observer.observe(sectionRef.current);
//     }

//     return () => observer.disconnect();
//   }, []);

//   return (
//     <section
//       ref={sectionRef}
//       className="py-20 bg-gradient-to-br from-[#131336] via-[#575E3F] to-[#8180A7] relative overflow-hidden"
//     >
//       {/* Background elements */}
//       <div className="absolute inset-0">
//         <div className="absolute top-20 left-20 w-64 h-64 bg-[#7DBE1D] rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
//         <div className="absolute bottom-20 right-20 w-64 h-64 bg-[#8180A7] rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-1000"></div>
//       </div>

//       <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="text-center mb-16">
//           <h2 className="text-4xl sm:text-5xl font-bold text-white mb-4">
//             How It Works
//           </h2>
//           <p className="text-xl text-gray-300 max-w-3xl mx-auto">
//             From concept to launch in just a few simple steps. Our AI handles the complexity while you focus on your vision.
//           </p>
//         </div>

//         <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
//           {steps.map((step, index) => (
//             <div key={index} className="step-card opacity-0 transform translate-y-8 relative">
//               {/* Connecting line */}
//               {index < steps.length - 1 && (
//                 <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-white/20 to-transparent z-0"></div>
//               )}

//               <div className="relative z-10 bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300 group">
//                 <div className="text-center">
//                   <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${step.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
//                     <step.icon className="w-8 h-8 text-white" />
//                   </div>

//                   <div className="absolute -top-4 -right-4 bg-[#7DBE1D] text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center shadow-md">
//                     {index + 1}
//                   </div>

//                   <h3 className="text-xl font-bold text-white mb-4">
//                     {step.title}
//                   </h3>

//                   <p className="text-gray-300 leading-relaxed">
//                     {step.description}
//                   </p>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>

//         <div className="text-center mt-16">
//           <button className="group relative bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
//             <span>Get Started Today</span>
//             <div className="absolute inset-0 bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] rounded-xl blur opacity-0 group-hover:opacity-75 transition-opacity duration-300 -z-10"></div>
//           </button>
//         </div>
//       </div>
//     </section>
//   );
// };

// export default HowItWorks;



//with responsive design

import React, { useEffect, useRef } from 'react';
import { MessageSquare, Wand2, Rocket, CheckCircle } from 'lucide-react';

const steps = [
  {
    icon: MessageSquare,
    title: 'Describe Your Vision',
    description: 'Tell our AI what kind of website you want to build. Share your ideas, goals, and preferences in natural language.',
    color: 'from-[#7DBE1D] to-[#C5DB9D]'
  },
  {
    icon: Wand2,
    title: 'AI Creates Your Site',
    description: 'Our intelligent system analyzes your requirements and generates a complete WordPress site with custom design and content.',
    color: 'from-[#8180A7] to-[#C5DB9D]'
  },
  {
    icon: CheckCircle,
    title: 'Customize & Perfect',
    description: 'Fine-tune your site with our intuitive editor. Adjust colors, layouts, content, and features to match your vision perfectly.',
    color: 'from-[#7DBE1D] to-[#8180A7]'
  },
  {
    icon: Rocket,
    title: 'Launch & Grow',
    description: 'Deploy your site with one click and start growing your business. Built-in SEO and performance optimization included.',
    color: 'from-[#C5DB9D] to-[#8180A7]'
  }
];

const HowItWorks: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const steps = entry.target.querySelectorAll('.step-card');
            steps.forEach((step, index) => {
              setTimeout(() => {
                step.classList.add('animate-fade-in-up');
              }, index * 200);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gradient-to-br from-[#131336] via-[#575E3F] to-[#8180A7] relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-[#7DBE1D] rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-64 h-64 bg-[#8180A7] rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            How It Works
          </h2>
          <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-300 max-w-3xl mx-auto">
            From concept to launch in just a few simple steps. Our AI handles the complexity while you focus on your vision.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="step-card opacity-0 transform translate-y-8 relative h-full">
              {/* Connecting line */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-white/20 to-transparent z-0"></div>
              )}

              <div className="relative z-10 bg-white/10 backdrop-blur-sm rounded-2xl p-6 sm:p-6 md:p-8 border border-white/20 hover:bg-white/15 transition-all duration-300 group h-full flex flex-col justify-between">
                <div className="text-center flex flex-col h-full">
                  <div className={`inline-flex items-center justify-center w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-gradient-to-r ${step.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <step.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                  </div>

                  <div className="absolute -top-4 -right-4 bg-[#7DBE1D] text-white text-xs sm:text-sm font-bold w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center shadow-md">
                    {index + 1}
                  </div>

                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-4">
                    {step.title}
                  </h3>

                  <p className="text-sm sm:text-base md:text-lg text-gray-300 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <button className="group relative bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg md:text-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
            <span>Get Started Today</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] rounded-xl blur opacity-0 group-hover:opacity-75 transition-opacity duration-300 -z-10"></div>
          </button>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
