// import React from 'react';
// import { Globe, Twitter, Github, Linkedin, Mail } from 'lucide-react';

// const Footer: React.FC = () => {
//   return (
//     <footer className="bg-gray-900 text-white">
//       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
//           {/* Company Info */}
//           <div className="space-y-4">
//             <div className="flex items-center space-x-2">
//               <Globe className="w-8 h-8 text-purple-400" />
//               <span className="text-xl font-bold">AI WordPress Builder</span>
//             </div>
//             <p className="text-gray-400 leading-relaxed">
//               Transform your ideas into stunning WordPress websites with the power of artificial intelligence. No coding required.
//             </p>
//             <div className="flex space-x-4">
//               <a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300">
//                 <Twitter className="w-5 h-5" />
//               </a>
//               <a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300">
//                 <Github className="w-5 h-5" />
//               </a>
//               <a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300">
//                 <Linkedin className="w-5 h-5" />
//               </a>
//               <a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300">
//                 <Mail className="w-5 h-5" />
//               </a>
//             </div>
//           </div>

//           {/* Product */}
//           <div>
//             <h3 className="text-lg font-semibold mb-4">Product</h3>
//             <ul className="space-y-2">
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Features</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Pricing</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Templates</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Integrations</a></li>
//             </ul>
//           </div>

//           {/* Company */}
//           <div>
//             <h3 className="text-lg font-semibold mb-4">Company</h3>
//             <ul className="space-y-2">
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">About</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Blog</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Careers</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Contact</a></li>
//             </ul>
//           </div>

//           {/* Support */}
//           <div>
//             <h3 className="text-lg font-semibold mb-4">Support</h3>
//             <ul className="space-y-2">
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Help Center</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Documentation</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Community</a></li>
//               <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Status</a></li>
//             </ul>
//           </div>
//         </div>

//         <div className="border-t border-gray-800 mt-12 pt-8">
//           <div className="flex flex-col md:flex-row justify-between items-center">
//             <p className="text-gray-400 text-sm">
//               © 2025 AI WordPress Builder. All rights reserved.
//             </p>
//             <div className="flex space-x-6 mt-4 md:mt-0">
//               <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
//                 Privacy Policy
//               </a>
//               <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
//                 Terms of Service
//               </a>
//               <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
//                 Cookie Policy
//               </a>
//             </div>
//           </div>
//         </div>
//       </div>
//     </footer>
//   );
// };

// export default Footer;


import React from 'react';
import { Globe, Twitter, Github, Linkedin, Mail } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-[#131336] text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Globe className="w-8 h-8 text-[#C5DB9D]" />
              <span className="text-xl font-bold text-[#C5DB9D]">AI WordPress Builder</span>
            </div>
            <p className="text-gray-400 leading-relaxed">
              Transform your ideas into stunning WordPress websites with the power of artificial intelligence. No coding required.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-[#C5DB9D] transition-colors duration-300">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-[#C5DB9D] transition-colors duration-300">
                <Github className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-[#C5DB9D] transition-colors duration-300">
                <Linkedin className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-[#C5DB9D] transition-colors duration-300">
                <Mail className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Product */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-[#C5DB9D]">Product</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Features</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Pricing</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Templates</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Integrations</a></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-[#C5DB9D]">Company</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">About</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Blog</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Careers</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Contact</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-[#C5DB9D]">Support</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Help Center</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Documentation</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Community</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors duration-300">Status</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-[#6D6D81] mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 AI WordPress Builder. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-300">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
