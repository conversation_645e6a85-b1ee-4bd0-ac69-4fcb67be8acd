import React, { useEffect, useRef } from 'react';
import { Star, Quote } from 'lucide-react';

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Small Business Owner',
    company: 'Bloom Bakery',
    image: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?w=150&h=150&fit=crop&crop=face',
    content: 'I went from idea to a fully functional bakery website in just 30 minutes. The AI understood exactly what I needed and created something beautiful.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Marketing Director',
    company: 'TechFlow Solutions',
    image: 'https://images.pexels.com/photos/697509/pexels-photo-697509.jpeg?w=150&h=150&fit=crop&crop=face',
    content: 'The AI-powered design capabilities are incredible. It created a professional site that rivals what we would have paid thousands for.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Freelance Designer',
    company: 'Creative Studio',
    image: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?w=150&h=150&fit=crop&crop=face',
    content: 'As a designer, I was skeptical about AI tools. But this platform actually enhanced my workflow and gave me ideas I never considered.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Restaurant Owner',
    company: 'Urban Eats',
    image: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=150&h=150&fit=crop&crop=face',
    content: 'The mobile-responsive design is perfect. Our customers love how easy it is to browse our menu and make reservations on any device.',
    rating: 5
  },
  {
    name: 'Lisa Thompson',
    role: 'E-commerce Entrepreneur',
    company: 'Fashion Forward',
    image: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=150&h=150&fit=crop&crop=face',
    content: 'The SEO optimization is outstanding. We saw a 300% increase in organic traffic within the first month of launching our new site.',
    rating: 5
  },
  {
    name: 'James Wilson',
    role: 'Non-profit Director',
    company: 'Community Care',
    image: 'https://images.pexels.com/photos/556667/pexels-photo-556667.jpeg?w=150&h=150&fit=crop&crop=face',
    content: 'For a non-profit with limited budget, this tool was a game-changer. We got a professional website that helps us connect with donors.',
    rating: 5
  }
];

const Testimonials: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('.testimonial-card');
            cards.forEach((card, index) => {
              setTimeout(() => {
                card.classList.add('animate-fade-in-up');
              }, index * 150);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-[#131336] via-[#575E3F] to-[#8180A7]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Loved by{' '}
            <span className="bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] bg-clip-text text-transparent">
              Thousands
            </span>
          </h2>
          <p className="text-base sm:text-lg text-gray-300 max-w-3xl mx-auto">
            Join thousands of business owners, entrepreneurs, and creators who have transformed their online presence with our AI-powered platform.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="testimonial-card opacity-0 transform translate-y-8 bg-white/10 backdrop-blur-sm text-white rounded-xl sm:rounded-2xl p-5 sm:p-8 text-sm sm:text-base hover:shadow-lg transition-all duration-500 group border border-white/10 hover:border-[#7DBE1D]/50"
            >
              <div className="flex items-center mb-6">
                <Quote className="w-6 h-6 sm:w-8 sm:h-8 text-[#7DBE1D] mb-4" />
              </div>

              <p className="text-gray-200 leading-relaxed mb-6 group-hover:text-white transition-colors duration-300">
                "{testimonial.content}"
              </p>

              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              <div className="flex items-center">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover mr-4 border-2 border-white/20"
                />
                <div>
                  <h4 className="font-semibold text-white">{testimonial.name}</h4>
                  <p className="text-sm text-gray-300">
                    {testimonial.role} at {testimonial.company}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12 sm:mt-16">
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-[#7DBE1D] to-[#C5DB9D] rounded-full px-4 py-2 sm:px-6 sm:py-3 shadow-lg text-xs sm:text-sm">
            <div className="flex -space-x-2">
              {testimonials.slice(0, 4).map((testimonial, index) => (
                <img
                  key={index}
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 border-white object-cover"
                />
              ))}
            </div>
            <span className="text-[#131336] font-medium">
              Join 10,000+ happy customers
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
