// //previous working
// 'use client';
// import React, { createContext, useState } from 'react';
// import { DEFAULT_THEME } from './constants';
// import { ThemeSettings } from "./types";

// export const CustomizationContext = createContext<{
//   theme: ThemeSettings;
//   updateTheme: (key: keyof ThemeSettings, value: string) => void;
// }>({
//   theme: DEFAULT_THEME,
//   updateTheme: () => {},
// });

// export const CustomizationProvider = ({ children }: { children: React.ReactNode }) => {
//   const [theme, setTheme] = useState<ThemeSettings>(DEFAULT_THEME);

//   const updateTheme = (key: keyof ThemeSettings, value: string) => {
//     setTheme(prev => ({ ...prev, [key]: value }));
//   };

//   return (
//     <CustomizationContext.Provider value={{ theme, updateTheme }}>
//       {children}
//     </CustomizationContext.Provider>
//   );
// };


// // Add this hook at the bottom of the file
// export const useCustomization = () => {
//   const context = React.useContext(CustomizationContext);
//   if (!context) {
//     throw new Error('useCustomization must be used within a CustomizationProvider');
//   }
//   return context;
// };

//new for updating variations

// CustomizationProvider.tsx
'use client';
import React, { createContext, useState } from 'react';
import { DEFAULT_THEME } from './constants';
import { ThemeSettings } from './types';

export const CustomizationContext = createContext<{
  theme: ThemeSettings;
  updateTheme: (key: keyof ThemeSettings, value: string | string[]) => void;
  setTheme: React.Dispatch<React.SetStateAction<ThemeSettings>>;
}>({
  theme: DEFAULT_THEME,
  updateTheme: () => {},
  setTheme: () => {},
});

export const CustomizationProvider = ({ children }: { children: React.ReactNode }) => {
  const [theme, setTheme] = useState<ThemeSettings>(DEFAULT_THEME);

  const updateTheme = (key: keyof ThemeSettings, value: string | string[]) => {
    setTheme(prev => ({ ...prev, [key]: value }));
  };

  return (
    <CustomizationContext.Provider value={{ theme, updateTheme, setTheme }}>
      {children}
    </CustomizationContext.Provider>
  );
};

export const useCustomization = () => {
  const context = React.useContext(CustomizationContext);
  if (!context) {
    throw new Error('useCustomization must be used within a CustomizationProvider');
  }
  return context;
};
