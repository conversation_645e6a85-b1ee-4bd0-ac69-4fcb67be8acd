// 'use client';

// import { useCustomization } from './CustomizationProvider';
// import { Button } from '@/components/ui/button';
// import { ThemeSettings } from './types';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';
// import { FiImage } from 'react-icons/fi';
// import { FaPalette } from 'react-icons/fa';

// export default function ThemeControlPanel({
//   sitePath,
//   onNext,
//   onBack,
//   showStyles,
//   setShowStyles,
//   showImages,
//   setShowImages,
// }: {
//   sitePath: string;
//   onNext: (theme: ThemeSettings) => void;
//   onBack: () => void;
//   showStyles: boolean;
//   setShowStyles: React.Dispatch<React.SetStateAction<boolean>>;
//   showImages: boolean;
//   setShowImages: React.Dispatch<React.SetStateAction<boolean>>;
// }) {
//   const { theme } = useCustomization();
//   const { formData } = useSiteBuilder();

//   const sendThemeUpdate = async (newTheme: typeof theme) => {
//     try {
//       const blogId = sitePath.match(/design-(\d+)/)?.[1] || '1';
//       const response = await fetch(`${sitePath}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ ...newTheme, blog_id: blogId }),
//       });

//       if (!response.ok) throw new Error('Failed to update theme settings');

//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
//         url.searchParams.set('primaryColor', newTheme.primaryColor);
//         url.searchParams.set('headingFont', newTheme.headingFont);
//         url.searchParams.set('bodyFont', newTheme.bodyFont);
//         url.searchParams.set('heroImageUrl', formData.images[0]?.url || '');
//         url.searchParams.set('headerImageUrl', formData.images[1]?.url || '');
//         url.searchParams.set('logoUrl', theme.logoUrl || '');
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }
//     } catch (err) {
//       console.error('Failed to update theme settings:', err);
//     }
//   };

//   return (
//     <div className="w-72 h-full bg-[#1e1e1e] text-white p-6 border-r border-gray-800 flex flex-col justify-between">
//       <div className="flex flex-col justify-center items-center flex-1 space-y-6">
//         <h2 className="text-xl font-bold text-white text-center mb-4">Design</h2>

//         <button
//           onClick={() => {
//             setShowImages((prev) => !prev);
//             setShowStyles(false);
//           }}
//           className="flex items-center gap-2 text-white hover:text-indigo-400 transition"
//         >
//           <FiImage /> Images
//         </button>

//         <button
//           onClick={() => {
//             setShowStyles((prev) => !prev);
//             setShowImages(false);
//           }}
//           className="flex items-center gap-2 text-white hover:text-indigo-400 transition"
//         >
//           <FaPalette /> Styles
//         </button>
//       </div>

//       <div className="space-y-3 pt-6">
//         <Button
//           className="w-full bg-indigo-600 hover:bg-indigo-700"
//           onClick={() => onNext(theme)}
//         >
//           Continue →
//         </Button>
//         <Button
//           className="w-full text-white border-gray-500 hover:bg-gray-800"
//           onClick={onBack}
          
//         >
//           Back to Other Designs
//         </Button>
//       </div>
//     </div>
//   );
// }


//below is the complete code for ThemeControlPanel.tsx and this is working fine.

// 'use client';

// import { useCustomization } from './CustomizationProvider';
// import { Button } from '@/components/ui/button';
// import { ThemeSettings } from './types';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';
// import { FiImage, FiChevronLeft } from 'react-icons/fi';
// import { FaPalette } from 'react-icons/fa';
// import { FiMonitor, FiTablet, FiSmartphone } from 'react-icons/fi';
// import ImagePanel from './ImagePanel';

// export default function ThemeControlPanel({
//   sitePath,
//   onNext,
//   onBack,
//   showStyles,
//   setShowStyles,
//   showImages,
//   setShowImages,
//   toggleSidebar,
//   device,
//   setDevice,
// }: {
//   sitePath: string;
//   onNext: (theme: ThemeSettings) => void;
//   onBack: () => void;
//   showStyles: boolean;
//   setShowStyles: React.Dispatch<React.SetStateAction<boolean>>;
//   showImages: boolean;
//   setShowImages: React.Dispatch<React.SetStateAction<boolean>>;
//   toggleSidebar: () => void;
//   device: 'desktop' | 'tablet' | 'mobile';
//   setDevice: React.Dispatch<React.SetStateAction<'desktop' | 'tablet' | 'mobile'>>;
// }) {
//   const { theme } = useCustomization();
//   const { formData } = useSiteBuilder();

//   const sendThemeUpdate = async (newTheme: typeof theme) => {
//     try {
//       const blogId = sitePath.match(/design-(\d+)/)?.[1] || '1';
//       const response = await fetch(`${sitePath}/wp-json/custom/v1/update-theme-settings`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ ...newTheme, blog_id: blogId }),
//       });

//       if (!response.ok) throw new Error('Failed to update theme settings');

//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
//         url.searchParams.set('primaryColor', newTheme.primaryColor);
//         url.searchParams.set('headingFont', newTheme.headingFont);
//         url.searchParams.set('bodyFont', newTheme.bodyFont);
//         url.searchParams.set('heroImageUrl', formData.images[0]?.url || '');
//         url.searchParams.set('headerImageUrl', formData.images[1]?.url || '');
//         url.searchParams.set('logoUrl', theme.logoUrl || '');
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }
//     } catch (err) {
//       console.error('Failed to update theme settings:', err);
//     }
//   };

//   return (
//     <div className="h-full w-full sm:w-72 bg-[#1e1e1e] text-white p-6 border-r border-gray-800 flex flex-col justify-between relative">
//       {/* Collapse Button */}
//       <button
//         onClick={toggleSidebar}
//         className="absolute top-3 right-3 bg-gray-700 hover:bg-gray-600 text-white p-1 rounded-full transition"
//         title="Collapse panel"
//       >
//         <FiChevronLeft size={18} />
//       </button>

//       {/* Main Controls */}
//       <div className="flex flex-col justify-center items-center flex-1 space-y-6 mt-6">
//         <h2 className="text-xl font-bold text-white text-center mb-4">Design</h2>

//         <button
//           onClick={() => {
//             setShowImages((prev) => !prev);
//             setShowStyles(false);
//           }}
//           className="flex items-center gap-2 text-white hover:text-indigo-400 transition"
//         >
//           <FiImage /> Images
//         </button>

//         <button
//           onClick={() => {
//             setShowStyles((prev) => !prev);
//             setShowImages(false);
//           }}
//           className="flex items-center gap-2 text-white hover:text-indigo-400 transition"
//         >
//           <FaPalette /> Styles
//         </button>
//       </div>

//       {/* Action Buttons */}
//       <div className="space-y-3 pt-6">
//         <Button
//           className="w-full bg-indigo-600 hover:bg-indigo-700"
//           onClick={() => onNext(theme)}
//         >
//           Continue →
//         </Button>
//         <Button
//           className="w-full text-white border-gray-500 hover:bg-gray-800"
//           onClick={onBack}
//         >
//           Back to Other Designs
//         </Button>
//       </div>

//       {/* Responsive Controls */}
//       <div className="pt-6 border-t border-gray-700 mt-6">
//         <p className="text-sm text-gray-300 mb-2">Responsive design</p>
//         <div className="flex justify-between items-center space-x-2">
//           <button
//             onClick={() => setDevice('desktop')}
//             className={`p-2 rounded hover:bg-gray-700 ${device === 'desktop' ? 'bg-indigo-600 text-white' : 'text-gray-300'}`}
//             title="Desktop"
//           >
//             <FiMonitor size={18} />
//           </button>
//           <button
//             onClick={() => setDevice('tablet')}
//             className={`p-2 rounded hover:bg-gray-700 ${device === 'tablet' ? 'bg-indigo-600 text-white' : 'text-gray-300'}`}
//             title="Tablet"
//           >
//             <FiTablet size={18} />
//           </button>
//           <button
//             onClick={() => setDevice('mobile')}
//             className={`p-2 rounded hover:bg-gray-700 ${device === 'mobile' ? 'bg-indigo-600 text-white' : 'text-gray-300'}`}
//             title="Mobile"
//           >
//             <FiSmartphone size={18} />
//           </button>
//         </div>
//       </div>


//     </div>
//   );
// }


//this is code with overlapping the ImagePanel and StylesPanel


'use client';

import { useCustomization } from './CustomizationProvider';
import { Button } from '@/components/ui/button';
import { ThemeSettings } from './types';
import { useSiteBuilder } from '@/context/SiteBuilderContext';
import { FiImage, FiChevronLeft } from 'react-icons/fi';
import { FaPalette } from 'react-icons/fa';
import { FiMonitor, FiTablet, FiSmartphone } from 'react-icons/fi';
import ImagePanel from './ImagePanel';
import StylesPanelWrapper from './StylesPanelWrapper';

export default function ThemeControlPanel({
  sitePath,
  onNext,
  onBack,
  showStyles,
  setShowStyles,
  showImages,
  setShowImages,
  toggleSidebar,
  device,
  setDevice,
}: {
  sitePath: string;
  onNext: (theme: ThemeSettings) => void;
  onBack: () => void;
  showStyles: boolean;
  setShowStyles: React.Dispatch<React.SetStateAction<boolean>>;
  showImages: boolean;
  setShowImages: React.Dispatch<React.SetStateAction<boolean>>;
  toggleSidebar: () => void;
  device: 'desktop' | 'tablet' | 'mobile';
  setDevice: React.Dispatch<React.SetStateAction<'desktop' | 'tablet' | 'mobile'>>;
}) {
  const { theme } = useCustomization();
  const { formData } = useSiteBuilder();

  const sendThemeUpdate = async (newTheme: typeof theme) => {
    try {
      const blogId = sitePath.match(/design-(\d+)/)?.[1] || '1';
      const response = await fetch(`${sitePath}/wp-json/custom/v1/update-theme-settings`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...newTheme, blog_id: blogId }),
      });

      if (!response.ok) throw new Error('Failed to update theme settings');

      const iframe = document.querySelector<HTMLIFrameElement>('iframe');
      if (iframe) {
        const url = new URL(iframe.src);
        url.searchParams.set('primaryColor', newTheme.primaryColor);
        url.searchParams.set('headingFont', newTheme.headingFont);
        url.searchParams.set('bodyFont', newTheme.bodyFont);
        url.searchParams.set('heroImageUrl', formData.images[0]?.url || '');
        url.searchParams.set('headerImageUrl', formData.images[1]?.url || '');
        url.searchParams.set('logoUrl', theme.logoUrl || '');
        url.searchParams.set('_refresh', Date.now().toString());
        iframe.src = url.toString();
      }
    } catch (err) {
      console.error('Failed to update theme settings:', err);
    }
  };

  return (
    <div className="relative h-full w-full sm:w-72 bg-[#1e1e1e] text-white p-6 border-r border-gray-800 flex flex-col justify-between">
      {/* Collapse Button */}
      <button
        onClick={toggleSidebar}
        className="absolute top-3 right-3 bg-gray-700 hover:bg-gray-600 text-white p-1 rounded-full transition"
        title="Collapse panel"
      >
        <FiChevronLeft size={18} />
      </button>

      {/* Main Controls */}
      <div className="flex flex-col justify-center items-center flex-1 space-y-6 mt-6">
        <h2 className="text-xl font-bold text-white text-center mb-4">Design</h2>

        <button
          onClick={() => {
            setShowImages(true);     // Show ImagePanel
            setShowStyles(false);
          }}
          className="flex items-center gap-2 text-white hover:text-indigo-400 transition"
        >
          <FiImage /> Images
        </button>

        <button
          onClick={() => {
            setShowStyles((prev) => !prev);
            setShowImages(false);
          }}
          className="flex items-center gap-2 text-white hover:text-indigo-400 transition"
        >
          <FaPalette /> Styles
        </button>
      </div>

      {/* Action Buttons */}
      <div className="space-y-3 pt-6">
        <Button className="w-full bg-indigo-600 hover:bg-indigo-700" onClick={() => onNext(theme)}>
          Continue →
        </Button>
        <Button className="w-full text-white border-gray-500 hover:bg-gray-800" onClick={onBack}>
          Back to Other Designs
        </Button>
      </div>

      {/* Responsive Controls */}
      <div className="pt-6 border-t border-gray-700 mt-6">
        <p className="text-sm text-gray-300 mb-2">Responsive design</p>
        <div className="flex justify-between items-center space-x-2">
          <button
            onClick={() => setDevice('desktop')}
            className={`p-2 rounded hover:bg-gray-700 ${device === 'desktop' ? 'bg-indigo-600 text-white' : 'text-gray-300'}`}
            title="Desktop"
          >
            <FiMonitor size={18} />
          </button>
          <button
            onClick={() => setDevice('tablet')}
            className={`p-2 rounded hover:bg-gray-700 ${device === 'tablet' ? 'bg-indigo-600 text-white' : 'text-gray-300'}`}
            title="Tablet"
          >
            <FiTablet size={18} />
          </button>
          <button
            onClick={() => setDevice('mobile')}
            className={`p-2 rounded hover:bg-gray-700 ${device === 'mobile' ? 'bg-indigo-600 text-white' : 'text-gray-300'}`}
            title="Mobile"
          >
            <FiSmartphone size={18} />
          </button>
        </div>
      </div>

      {/* Mobile-only ImagePanel Overlay */}
      {showImages && (
        <div className="absolute inset-0 z-10 bg-white text-black sm:hidden">
          <ImagePanel onBack={() => setShowImages(false)} />
        </div>
      )}

      {/* Mobile-only StylesPanel Overlay */}
      {showStyles && (
        <div className="absolute inset-0 z-10 bg-white text-black sm:hidden">
          <div className="flex items-center px-4 py-3 border-b border-gray-300">
            <button
              onClick={() => setShowStyles(false)}
              className="text-gray-700 hover:text-black flex items-center gap-1"
            >
              <FiChevronLeft /> Back
            </button>
            <h2 className="ml-4 text-base font-semibold">Styles</h2>
          </div>
          <StylesPanelWrapper />
        </div>
      )}
    </div>
  );
}
