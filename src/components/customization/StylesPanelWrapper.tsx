//with applying variation on the theme
'use client';

import React, { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import StylesPanel from './StylesPanel';
import BrowseStyles from './BrowseStyles';
import TypographyPanel from './TypographyPanel';
import ColorPanel from './ColorPanel';
import EditPalettePanel from './EditPalettePanel';
import { useCustomization } from './CustomizationProvider';

type Panel = 'styles' | 'browse' | 'typography' | 'colors' | 'editPalette';

export default function StylesPanelWrapper() {
  const [currentPanel, setCurrentPanel] = useState<Panel>('styles');
  const { theme } = useCustomization();

  const renderPanel = () => {
    switch (currentPanel) {
      case 'browse':
        return <BrowseStyles onBack={() => setCurrentPanel('styles')} />;
      case 'typography':
        return <TypographyPanel onBack={() => setCurrentPanel('styles')} />;
      case 'colors':
        return (
            <ColorPanel
            onBack={() => setCurrentPanel('styles')}
            onNavigateEditPalette={() => setCurrentPanel('editPalette')}
            selectedStyle={theme}
            />
        );
      case 'editPalette':
        return (
            <EditPalettePanel
            onBack={() => setCurrentPanel('colors')}
            selectedStyle={theme}
            />
        );
      default:
        return (
          <StylesPanel
            onNavigate={setCurrentPanel}
            selectedStyle={{
              name: theme.name || 'Current',
              bg: theme.bg || 'bg-gray-900',
              colors: theme.colors?.map(c => `bg-[${c}]`) || ['bg-white', 'bg-gray-500'],
            }}
          />
        );
    }
  };

  return (
    <div className="relative w-72 h-full overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentPanel}
          initial={{ x: 300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -300, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="absolute top-0 left-0 w-full h-full"
        >
          {renderPanel()}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
