// CustomizationStep.tsx
import React, { useRef } from 'react';
import { ThemeControls } from './ThemeControls';

export default function CustomizationStep({
  postId,
  siteName,
  brief,
  heroImage,
  headerImage,
  phone,
  email,
  themeSlug,
}: {
  postId: number;
  siteName: string;
  brief: string;
  heroImage: string;
  headerImage: string;
  phone: string;
  email: string;
  themeSlug: string;
}) {
  const previewRef = useRef<HTMLIFrameElement>(null);

  return (
    <div className="flex h-full">
      <div className="w-1/3 p-4">
        <ThemeControls previewRef={previewRef} />
        {/* Back/Continue buttons */}
      </div>
      <div className="w-2/3">
        <iframe
          ref={previewRef}
          src={`http://localhost/?id=${postId}&website_name=${siteName}&website_subtitle=${brief}&hero_image=${heroImage}&header_image=${headerImage}&phone=${phone}&email=${email}&preview_theme=${themeSlug}`}
          style={{ width: '100%', height: '100vh', border: 'none' }}
          title="Template Preview"
        />
      </div>
    </div>
  );
}
