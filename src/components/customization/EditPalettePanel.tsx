// 'use client';

// import React from 'react';
// import { FiChevronLeft } from 'react-icons/fi';

// const LABELS = ['Primary', 'Secondary', 'Heading', 'Body', 'Background'];

// export default function EditPalettePanel({
//   onBack,
//   selectedStyle,
// }: {
//   onBack: () => void;
//   selectedStyle: { name: string; bg: string; colors: string[] };
// }) {
//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col">
//       {/* Header */}
//       <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
//         <FiChevronLeft className="cursor-pointer" onClick={onBack} />
//         <span>Edit palette</span>
//       </div>

//       {/* Content */}
//       <div className="p-4">
//         <h3 className="text-sm font-semibold text-gray-700 mb-1">Color</h3>

//         <h4 className="text-xs font-bold text-gray-500 mb-2 mt-4">THEME</h4>
//         <div className="flex flex-wrap gap-3">
//           {selectedStyle.colors.map((color, index) => (
//             <div
//               key={index}
//               className={`relative w-8 h-8 rounded-full border border-gray-300 ${color} group`}
//             >
//               <div className="absolute -top-6 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition">
//                 {LABELS[index] || `Color ${index + 1}`}
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// }


// only displaying primarycolor and secondarycolor
// 'use client';

// import React from 'react';
// import { FiChevronLeft } from 'react-icons/fi';
// import { useCustomization } from './CustomizationProvider';

// const LABELS: Array<keyof ReturnType<typeof useCustomization>['theme']> = [
//   'primaryColor',
//   'secondaryColor',

// ];

// export default function EditPalettePanel({
//   onBack,
//   selectedStyle,
// }: {
//   onBack: () => void;
//   selectedStyle: { name: string; bg: string; colors: string[] };
// }) {
//   const { theme, updateTheme } = useCustomization();

//   const handleColorClick = (label: keyof typeof theme, colorClass: string) => {
//     updateTheme(label, colorClass);

//     // Live update preview iframe
//     const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//     if (iframe) {
//       const url = new URL(iframe.src);
//       url.searchParams.set(label, colorClass);
//       url.searchParams.set('_refresh', Date.now().toString());
//       iframe.src = url.toString();
//     }
//   };

//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col">
//       {/* Header */}
//       <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
//         <FiChevronLeft className="cursor-pointer" onClick={onBack} />
//         <span>Edit palette</span>
//       </div>

//       {/* Content */}
//       <div className="p-4">
//         <h4 className="text-xs font-bold text-gray-500 mb-2">THEME COLORS</h4>

//         <div className="flex flex-wrap gap-4">
//           {selectedStyle.colors.map((colorClass, index) => {
//             const themeKey = LABELS[index];
//             if (!themeKey) return null;

//             return (
//               <button
//                 key={themeKey}
//                 onClick={() => handleColorClick(themeKey, colorClass)}
//                 className="relative w-10 h-10 rounded-full border-2"
//                 style={{
//                   backgroundColor: colorClass,
//                   borderColor: theme[themeKey] === colorClass ? 'black' : 'transparent',
//                 }}
//                 title={themeKey}
//               >
//                 <span className="sr-only">{themeKey}</span>
//               </button>
//             );
//           })}
//         </div>
//       </div>
//     </div>
//   );
// }


//using all colors (without color picker)
// 'use client';

// import React from 'react';
// import { FiChevronLeft } from 'react-icons/fi';
// import { useCustomization } from './CustomizationProvider';

// const LABELS: Array<keyof ReturnType<typeof useCustomization>['theme']> = [
//   'primaryColor',
//   'secondaryColor',
//   'headingColor',
//   'textColor',
//   'backgroundColor',
// ];

// export default function EditPalettePanel({
//   onBack,
//   selectedStyle,
// }: {
//   onBack: () => void;
//   selectedStyle: { name: string; bg: string; colors: string[] };
// }) {
//   const { theme, updateTheme } = useCustomization();

//   const handleColorClick = (label: keyof typeof theme, colorValue: string) => {
//     updateTheme(label, colorValue);

//     const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//     if (iframe) {
//       const url = new URL(iframe.src);
//       url.searchParams.set(label, colorValue);
//       url.searchParams.set('_refresh', Date.now().toString());
//       iframe.src = url.toString();
//     }
//   };

//   // Build an ordered array of { label, color } based on LABELS
//   const colorItems: { label: keyof typeof theme; color: string }[] = LABELS.map(label => ({
//   label,
//   color: theme[label] as string,
// }));

//   return (
//     <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col">
//       {/* Header */}
//       <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
//         <FiChevronLeft className="cursor-pointer" onClick={onBack} />
//         <span>Edit palette</span>
//       </div>

//       {/* Content */}
//       <div className="p-4">
//         <h4 className="text-xs font-bold text-gray-500 mb-2">THEME COLORS</h4>
//         <div className="flex items-center gap-2">
//           {colorItems.map(({ label, color }, index) => (
//             <div key={index} className="relative group">
//               <button
//                 onClick={() => handleColorClick(label, color)}
//                 className="w-8 h-8 rounded-full border-2"
//                 style={{
//                   backgroundColor: color,
//                   borderColor: 'black',
//                 }}
//               >
//                 <span className="sr-only">{label}</span>
//               </button>
//               <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 rounded text-xs bg-black text-white opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
//                 {label}
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </div>
//   );
// }


//with color picker

'use client';

import React, { useState } from 'react';
import { FiChevronLeft } from 'react-icons/fi';
import { useCustomization } from './CustomizationProvider';
import { HexColorPicker } from 'react-colorful';

const LABELS: Array<keyof ReturnType<typeof useCustomization>['theme']> = [
  'primaryColor',
  'secondaryColor',
  'headingColor',
  'textColor',
  'backgroundColor',
];

export default function EditPalettePanel({
  onBack,
  selectedStyle,
}: {
  onBack: () => void;
  selectedStyle: { name: string; bg: string; colors: string[] };
}) {
  const { theme, updateTheme } = useCustomization();

  const [activePicker, setActivePicker] = useState<null | keyof typeof theme>(null);
  const [tempColor, setTempColor] = useState<string>('');

  const handleColorClick = (label: keyof typeof theme) => {
    setTempColor(theme[label] as string);
    setActivePicker(label);
  };

  const handleColorChange = (color: string) => {
    if (activePicker) {
      setTempColor(color);
      updateTheme(activePicker, color);

      const iframe = document.querySelector<HTMLIFrameElement>('iframe');
      if (iframe) {
        const url = new URL(iframe.src);
        url.searchParams.set(activePicker, color);
        url.searchParams.set('_refresh', Date.now().toString());
        iframe.src = url.toString();
      }
    }
  };

  const colorItems: { label: keyof typeof theme; color: string }[] = LABELS.map(label => ({
    label,
    color: theme[label] as string,
  }));

  return (
    <div className="h-full w-full bg-white text-black border-l border-gray-300 flex flex-col relative">
      {/* Header */}
      <div className="px-4 py-4 border-b border-gray-200 flex items-center gap-2 text-sm font-medium">
        <FiChevronLeft className="cursor-pointer" onClick={onBack} />
        <span>Edit palette</span>
      </div>

      {/* Content */}
      <div className="p-4">
        <h4 className="text-xs font-bold text-gray-500 mb-2">THEME COLORS</h4>
        <div className="flex items-center gap-2 flex-wrap">
          {colorItems.map(({ label, color }, index) => (
            <div key={index} className="relative group">
              <button
                onClick={() => handleColorClick(label)}
                className="w-8 h-8 rounded-full border-2"
                style={{
                  backgroundColor: color,
                  borderColor: 'black',
                }}
              >
                <span className="sr-only">{label}</span>
              </button>
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 rounded text-xs bg-black text-white opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                {label}
              </div>

              {/* Color Picker Popup */}
              {activePicker === label && (
                <div className="absolute z-10 top-10 left-1/2 -translate-x-1/2 bg-white p-3 rounded shadow-lg border">
                  <HexColorPicker color={tempColor} onChange={handleColorChange} />
                  <div className="mt-2 text-xs text-gray-700 text-center">{tempColor}</div>
                  <button
                    className="mt-2 text-xs text-blue-600 underline w-full"
                    onClick={() => setActivePicker(null)}
                  >
                    Close
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

