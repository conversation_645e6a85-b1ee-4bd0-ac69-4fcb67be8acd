// //this is working code. here, ai image generation is added seperately
// 'use client';

// import React, { useState } from 'react';
// import { useCustomization } from './CustomizationProvider';
// import { Button } from '@/components/ui/button';
// import { FiUpload } from 'react-icons/fi';
// import { FaMagic } from 'react-icons/fa';
// import { useSiteBuilder } from '@/context/SiteBuilderContext';

// import {
//   DndContext,
//   closestCenter,
//   PointerSensor,
//   useSensor,
//   useSensors,
// } from '@dnd-kit/core';
// import {
//   SortableContext,
//   useSortable,
//   arrayMove,
//   verticalListSortingStrategy,
// } from '@dnd-kit/sortable';
// import { CSS } from '@dnd-kit/utilities';

// function SortableImageItem({ id, url, label }: { id: string; url: string; label: string }) {
//   const {
//     attributes,
//     listeners,
//     setNodeRef,
//     transform,
//     transition,
//     isDragging,
//   } = useSortable({ id });

//   const style = {
//     transform: CSS.Transform.toString(transform),
//     transition,
//     cursor: 'grab',
//     opacity: isDragging ? 0.5 : 1,
//   };

//   return (
//     <div
//       ref={setNodeRef}
//       style={style}
//       {...attributes}
//       {...listeners}
//       className="flex items-center space-x-3 p-2 border border-gray-300 rounded-md bg-white"
//     >
//       <span className="text-sm text-gray-800 font-medium w-14">{label}</span>
//       <img
//         src={url}
//         alt={label}
//         className="w-12 h-12 object-cover rounded border border-gray-200"
//       />
//     </div>
//   );
// }

// export default function ImagePanel({ onBack }: { onBack?: () => void }) {
//   const { theme, updateTheme } = useCustomization();
//   const { formData, updateFormData } = useSiteBuilder();
//   const [aiPrompt, setAiPrompt] = useState('');

//   const [imageOrder, setImageOrder] = useState(formData.selectedImages);
//   const labels = ['Hero Image', 'Banner Image', 'Background Image', 'Image 4', 'Image 5'];

//   const sensors = useSensors(useSensor(PointerSensor));

//   // Update the handleDragEnd function in ImagePanel.tsx
//   const handleDragEnd = (event: any) => {
//     const { active, over } = event;
//     if (active.id !== over.id) {
//       const oldIndex = imageOrder.indexOf(active.id);
//       const newIndex = imageOrder.indexOf(over.id);
//       const newOrder = arrayMove(imageOrder, oldIndex, newIndex);
//       setImageOrder(newOrder);
//       updateFormData({ selectedImages: newOrder });
      
//       // Force update the preview with new image order
//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
        
//         // Update all image parameters in the URL
//         newOrder.forEach((imageUrl, index) => {
//           url.searchParams.set(`home-image${index + 1}`, imageUrl);
//         });
        
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }
//     }
//   };

//   const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (!file) return;
//     const formDataUpload = new FormData();
//     formDataUpload.append('file', file);

//     try {
//       const res = await fetch('/api/upload-to-wp', {
//         method: 'POST',
//         body: formDataUpload,
//       });
//       const data = await res.json();
//       if (!res.ok || !data?.url || !data?.id) throw new Error(data.error || 'Upload failed');

//       await fetch('/api/set-site-logo', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ logoImageId: data.id }),
//       });

//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
//         url.searchParams.set('logoUrl', data.url);
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }

//       updateTheme('logoUrl', data.url);
//       alert('Logo uploaded and set!');
//     } catch (err) {
//       console.error('Upload failed:', err);
//       alert('Upload failed.');
//     }
//   };

//   const handleFaviconUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0];
//     if (!file) return;

//     const formDataUpload = new FormData();
//     formDataUpload.append('file', file);

//     try {
//       const res = await fetch('/api/upload-to-wp', {
//         method: 'POST',
//         body: formDataUpload,
//       });
//       const data = await res.json();
//       if (!res.ok || !data?.url || !data?.id) throw new Error(data.error || 'Upload failed');

//       await fetch('/api/set-site-favicon', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ faviconImageId: data.id }),
//       });

//       const iframe = document.querySelector<HTMLIFrameElement>('iframe');
//       if (iframe) {
//         const url = new URL(iframe.src);
//         url.searchParams.set('faviconUrl', data.url);
//         url.searchParams.set('_refresh', Date.now().toString());
//         iframe.src = url.toString();
//       }

//       updateTheme('faviconUrl', data.url);
//       alert('Favicon uploaded and set!');
//     } catch (err) {
//       console.error('Favicon upload failed:', err);
//       alert('Favicon upload failed.');
//     }
//   };

//   const handleGenerateAI = () => {
//     alert(`Generating image for prompt: "${aiPrompt}"`);
//   };

//   return (
//     <div className="h-full w-72 bg-white text-black border-l border-gray-300 flex flex-col">
//       {/* Mobile back button */}
//       {onBack && (
//         <div className="sm:hidden px-4 py-3 border-b border-gray-300 flex items-center">
//           <button
//             onClick={onBack}
//             className="text-gray-700 hover:text-indigo-600 text-lg font-semibold"
//           >
//             ← Back
//           </button>
//         </div>
//       )}
//       {/* Header */}
//       <div className="px-4 py-4 border-b border-gray-200">
//         <h2 className="text-lg font-semibold">Images</h2>
//       </div>

//       {/* Content */}
//       <div className="flex-1 px-4 py-6 space-y-6 overflow-y-auto">
//         {/* Upload Logo */}
//         <div>
//           <h3 className="text-sm text-gray-700 mb-2 flex items-center gap-2">
//             <FiUpload /> Upload Logo
//           </h3>
//           <input type="file" id="logo-upload" onChange={handleLogoUpload} className="hidden" />
//           <label
//             htmlFor="logo-upload"
//             className="block border border-gray-400 rounded px-3 py-2 text-center cursor-pointer hover:bg-gray-100"
//           >
//             Upload Logo
//           </label>
//           {theme.logoUrl && (
//             <img src={theme.logoUrl} alt="Logo" className="h-10 mt-2 mx-auto" />
//           )}
//         </div>

//         {/* Upload Favicon */}
//         <div>
//           <h3 className="text-sm text-gray-700 mb-2 flex items-center gap-2">
//             <FiUpload /> Upload Favicon
//           </h3>
//           <p className="text-xs text-gray-500 mb-2">
//             *Favicon appears in the browser tab next to your website name.
//           </p>
//           <input type="file" id="favicon-upload" onChange={handleFaviconUpload} className="hidden" />
//           <label
//             htmlFor="favicon-upload"
//             className="block border border-gray-400 rounded px-3 py-2 text-center cursor-pointer hover:bg-gray-100"
//           >
//             Upload Favicon
//           </label>
//           {theme.faviconUrl && (
//             <img src={theme.faviconUrl} alt="Favicon" className="h-6 mt-2 mx-auto" />
//           )}
//         </div>

//         {/* AI Image Generator */}
//         <div>
//           <h3 className="text-sm text-gray-700 mb-2 flex items-center gap-2">
//             <FaMagic /> AI Image Generator
//           </h3>
//           <input
//             type="text"
//             placeholder="Enter your prompt"
//             value={aiPrompt}
//             onChange={(e) => setAiPrompt(e.target.value)}
//             className="w-full mb-2 px-3 py-2 text-sm border border-gray-300 rounded"
//           />
//           <Button
//             onClick={handleGenerateAI}
//             className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
//           >
//             Generate with AI
//           </Button>
//         </div>

//         {/* Your Selected Images */}
//         {imageOrder.length > 0 && (
//           <div className="mt-6">
//             <h3 className="text-sm text-gray-700 mb-3 font-medium">Your Images</h3>
//             <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
//               <SortableContext items={imageOrder} strategy={verticalListSortingStrategy}>
//                 <div className="space-y-3">
//                   {imageOrder.map((url, idx) => (
//                     <SortableImageItem
//                       key={url}
//                       id={url}
//                       url={url}
//                       label={`Image ${idx + 1}`}
//                     />
//                   ))}
//                 </div>
//               </SortableContext>
//             </DndContext>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }



//this code does not include the image generation section but include image generation in separate for each image

'use client';

import React, { useState } from 'react';
import { useCustomization } from './CustomizationProvider';
import { Button } from '@/components/ui/button';
import { FiUpload } from 'react-icons/fi';
import { FaMagic } from 'react-icons/fa';
import { useSiteBuilder } from '@/context/SiteBuilderContext';

import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

function SortableImageItem({
  id,
  url,
  label,
  onAIGenerate,
}: {
  id: string;
  url: string;
  label: string;
  onAIGenerate: () => void;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: 'default', // changed from 'grab'
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between p-2 border border-gray-300 rounded-md bg-white"
    >
      <div className="flex items-center space-x-3">
        {/* Drag handle only on label to avoid overriding button click */}
        <span
          {...attributes}
          {...listeners}
          className="text-sm text-gray-800 font-medium w-14 cursor-grab"
        >
          {label}
        </span>
        <img
          src={url}
          alt={label}
          className="w-12 h-12 object-cover rounded border border-gray-200"
        />
      </div>

      {/* ✅ Button works now */}
      <button
        onClick={(e) => {
          e.stopPropagation(); // just in case
          onAIGenerate();
        }}
        className="text-indigo-500 hover:text-indigo-700"
      >
        <FaMagic className="w-4 h-4" />
      </button>
    </div>
  );
}


export default function ImagePanel({ onBack }: { onBack?: () => void }) {
  const { theme, updateTheme } = useCustomization();
  const { formData, updateFormData } = useSiteBuilder();
  const [modalType, setModalType] = useState<'logo' | 'favicon' | 'image' | null>(null);
  const [modalImageIndex, setModalImageIndex] = useState<number | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalPrompt, setModalPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const [imageOrder, setImageOrder] = useState(formData.selectedImages);
  const sensors = useSensors(useSensor(PointerSensor));

  const openModal = (type: 'logo' | 'favicon' | 'image', imageIndex?: number) => {
    setModalType(type);
    setModalImageIndex(imageIndex ?? null);
    setModalPrompt('');
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setModalType(null);
    setModalImageIndex(null);
    setModalPrompt('');
  };

  const handleGenerateAI = async () => {
    if (!modalType) return;
    setIsGenerating(true);

    try {
      const siteName = formData.name || 'My Website';
      const category = formData.category || 'Business';
      const brief = formData.description || '';
      const prompt = modalPrompt || '';

      const size =
        modalType === 'logo' || modalType === 'favicon'
          ? '512x512'
          : '1024x1024';

      const res = await fetch('/api/generate-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          siteName,
          category,
          brief,
          n: 1,
          size,
        }),
      });

      const data = await res.json();

      if (!res.ok || !data?.imageUrls?.[0]) throw new Error(data.error || 'Generation failed');

      const generatedImageUrl = data.imageUrls[0];

      if (modalType === 'logo') {
        updateTheme('logoUrl', generatedImageUrl);
        // Update iframe
        const iframe = document.querySelector<HTMLIFrameElement>('iframe');
        if (iframe) {
          const url = new URL(iframe.src);
          url.searchParams.set('logoUrl', generatedImageUrl);
          url.searchParams.set('_refresh', Date.now().toString());
          iframe.src = url.toString();
        }
      } else if (modalType === 'favicon') {
        updateTheme('faviconUrl', generatedImageUrl);
        // Update iframe
        const iframe = document.querySelector<HTMLIFrameElement>('iframe');
        if (iframe) {
          const url = new URL(iframe.src);
          url.searchParams.set('faviconUrl', generatedImageUrl);
          url.searchParams.set('_refresh', Date.now().toString());
          iframe.src = url.toString();
        }
      } else if (modalType === 'image' && modalImageIndex !== null) {
        // Create new arrays with the updated image
        const newSelectedImages = [...formData.selectedImages];
        newSelectedImages[modalImageIndex] = generatedImageUrl;
        
        // Add to uploadedImages if not already there
        const newUploadedImages = [...formData.uploadedImages];
        if (!newUploadedImages.includes(generatedImageUrl)) {
          newUploadedImages.push(generatedImageUrl);
        }

        // Update form data
        updateFormData({
          selectedImages: newSelectedImages,
          uploadedImages: newUploadedImages,
        });

        // Also update the local imageOrder state to trigger re-render
        setImageOrder(newSelectedImages);

        // Update iframe
        const iframe = document.querySelector<HTMLIFrameElement>('iframe');
        if (iframe) {
          const url = new URL(iframe.src);
          newSelectedImages.forEach((img, idx) => {
            url.searchParams.set(`home-image${idx + 1}`, img);
          });
          url.searchParams.set('_refresh', Date.now().toString());
          iframe.src = url.toString();
        }
      }

      closeModal();
    } catch (err) {
      console.error('[AI Generation Error]', err);
      alert('Image generation failed.');
    }
    finally {
      setIsGenerating(false); // Stop loading regardless of success/failure
      closeModal();
    }
  };


  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      const oldIndex = imageOrder.indexOf(active.id);
      const newIndex = imageOrder.indexOf(over.id);
      const newOrder = arrayMove(imageOrder, oldIndex, newIndex);
      setImageOrder(newOrder);
      updateFormData({ selectedImages: newOrder });

      const iframe = document.querySelector<HTMLIFrameElement>('iframe');
      if (iframe) {
        const url = new URL(iframe.src);
        newOrder.forEach((imageUrl, index) => {
          url.searchParams.set(`home-image${index + 1}`, imageUrl);
        });
        url.searchParams.set('_refresh', Date.now().toString());
        iframe.src = url.toString();
      }
    }
  };

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const formDataUpload = new FormData();
    formDataUpload.append('file', file);

    try {
      const res = await fetch('/api/upload-to-wp', {
        method: 'POST',
        body: formDataUpload,
      });
      const data = await res.json();
      if (!res.ok || !data?.url || !data?.id) throw new Error(data.error || 'Upload failed');

      await fetch('/api/set-site-logo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ logoImageId: data.id }),
      });

      const iframe = document.querySelector<HTMLIFrameElement>('iframe');
      if (iframe) {
        const url = new URL(iframe.src);
        url.searchParams.set('logoUrl', data.url);
        url.searchParams.set('_refresh', Date.now().toString());
        iframe.src = url.toString();
      }

      updateTheme('logoUrl', data.url);
      alert('Logo uploaded and set!');
    } catch (err) {
      console.error('Upload failed:', err);
      alert('Upload failed.');
    }
  };

  const handleFaviconUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const formDataUpload = new FormData();
    formDataUpload.append('file', file);

    try {
      const res = await fetch('/api/upload-to-wp', {
        method: 'POST',
        body: formDataUpload,
      });
      const data = await res.json();
      if (!res.ok || !data?.url || !data?.id) throw new Error(data.error || 'Upload failed');

      await fetch('/api/set-site-favicon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ faviconImageId: data.id }),
      });

      const iframe = document.querySelector<HTMLIFrameElement>('iframe');
      if (iframe) {
        const url = new URL(iframe.src);
        url.searchParams.set('faviconUrl', data.url);
        url.searchParams.set('_refresh', Date.now().toString());
        iframe.src = url.toString();
      }

      updateTheme('faviconUrl', data.url);
      alert('Favicon uploaded and set!');
    } catch (err) {
      console.error('Favicon upload failed:', err);
      alert('Favicon upload failed.');
    }
  };

  return (
    <div className="h-full w-72 bg-white text-black border-l border-gray-300 flex flex-col">
      {onBack && (
        <div className="sm:hidden px-4 py-3 border-b border-gray-300 flex items-center">
          <button
            onClick={onBack}
            className="text-gray-700 hover:text-indigo-600 text-lg font-semibold"
          >
            ← Back
          </button>
        </div>
      )}

      <div className="px-4 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold">Images</h2>
      </div>

      <div className="flex-1 px-4 py-6 space-y-6 overflow-y-auto">
        {/* Upload Logo */}
        <div>
          <h3 className="text-sm text-gray-700 mb-2 flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FiUpload /> Upload Logo
            </span>
            <button onClick={() => openModal('logo')} className="text-indigo-500 hover:text-indigo-700 text-xs flex items-center gap-1">
              <FaMagic className="w-3 h-3" /> Generate
            </button>
          </h3>
          <input type="file" id="logo-upload" onChange={handleLogoUpload} className="hidden" />
          <label
            htmlFor="logo-upload"
            className="block border border-gray-400 rounded px-3 py-2 text-center cursor-pointer hover:bg-gray-100"
          >
            Upload Logo
          </label>
          {theme.logoUrl && <img src={theme.logoUrl} alt="Logo" className="h-10 mt-2 mx-auto" />}
        </div>

        {/* Upload Favicon */}
        <div>
          <h3 className="text-sm text-gray-700 mb-2 flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FiUpload /> Upload Favicon
            </span>
            <button onClick={() => openModal('favicon')} className="text-indigo-500 hover:text-indigo-700 text-xs flex items-center gap-1">
              <FaMagic className="w-3 h-3" /> Generate
            </button>
          </h3>
          <p className="text-xs text-gray-500 mb-2">
            *Favicon appears in the browser tab next to your website name.
          </p>
          <input type="file" id="favicon-upload" onChange={handleFaviconUpload} className="hidden" />
          <label
            htmlFor="favicon-upload"
            className="block border border-gray-400 rounded px-3 py-2 text-center cursor-pointer hover:bg-gray-100"
          >
            Upload Favicon
          </label>
          {theme.faviconUrl && <img src={theme.faviconUrl} alt="Favicon" className="h-6 mt-2 mx-auto" />}
        </div>

        {/* Your Selected Images */}
        {imageOrder.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm text-gray-700 mb-3 font-medium">Your Images</h3>
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={imageOrder} strategy={verticalListSortingStrategy}>
                <div className="space-y-3">
                  {imageOrder.map((url, idx) => (
                    <SortableImageItem
                      key={url}
                      id={url}
                      url={url}
                      label={`Image ${idx + 1}`}
                      onAIGenerate={() => openModal('image', idx)}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}
      </div>

      {/* Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-80">
            <h2 className="text-lg font-semibold mb-2">
              {modalType === 'logo'
                ? 'Generate Logo'
                : modalType === 'favicon'
                ? 'Generate Favicon'
                : 'Generate Image'}
            </h2>
            <textarea
              className="w-full border border-gray-300 rounded p-2 text-sm mb-4"
              rows={3}
              placeholder="Enter prompt (optional)"
              value={modalPrompt}
              onChange={(e) => setModalPrompt(e.target.value)}
            />
            <div className="flex justify-end space-x-2">
              <Button variant="ghost" onClick={closeModal}>
                Cancel
              </Button>
              <Button 
                onClick={handleGenerateAI} 
                className="bg-indigo-600 hover:bg-indigo-700 text-white"
                disabled={isGenerating} // Disable button while generating
              >
                {isGenerating ? 'Generating...' : 'Generate'}
              </Button>

            </div>
          </div>
        </div>
      )}
    </div>
  );
}
