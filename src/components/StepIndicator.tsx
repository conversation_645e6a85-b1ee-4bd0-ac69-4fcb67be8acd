
// import React from 'react';
// import { Check } from 'lucide-react';
// import { useSiteBuilder } from 'src/context/SiteBuilderContext';

// interface StepIndicatorProps {
//   currentStep: number;
// }

// export const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep }) => {
//   const { goToStep } = useSiteBuilder();

//   const steps = [
//     { number: 1, title: 'Website Info' },
//     { number: 2, title: 'Description' },
//     { number: 3, title: 'Contact Info' },
//     { number: 4, title: 'Images' },
//     { number: 5, title: 'Templates' },
//     { number: 6, title: 'Customize' },
//     { number: 7, title: 'Generate' },
//   ];

//   return (
//     <div className="hidden md:block">
//       <div className="flex justify-between items-center">
//         {steps.map((step, index) => (
//           <React.Fragment key={step.number}>
//             <div
//               className="flex flex-col items-center cursor-pointer"
//               onClick={() => {
//                 if (step.number <= currentStep) {
//                   goToStep(step.number);
//                 }
//               }}
//             >
//               <div
//                 className={`w-10 h-10 flex items-center justify-center rounded-full transition-all duration-300 ${
//                   step.number < currentStep
//                     ? 'bg-[#7DBE1D] text-white'
//                     : step.number === currentStep
//                     ? 'bg-white border-2 border-[#7DBE1D] ring-4 ring-[#C5DB9D]/30 text-[#6D6D81]'
//                     : 'bg-gray-200 text-gray-500'
//                 }`}
//               >
//                 {step.number < currentStep ? (
//                   <Check className="w-5 h-5" />
//                 ) : (
//                   step.number
//                 )}
//               </div>
//               <div
//                 className={`mt-2 text-sm transition-all duration-300 ${
//                   step.number <= currentStep ? 'text-[#7DBE1D] font-medium' : 'text-gray-500'
//                 }`}
//               >
//                 {step.title}
//               </div>
//             </div>

//             {index < steps.length - 1 && (
//               <div
//                 className={`flex-grow h-1 mx-2 transition-colors duration-300 ${
//                   index < currentStep - 1 ? 'bg-[#C5DB9D]' : 'bg-gray-200'
//                 }`}
//               />
//             )}
//           </React.Fragment>
//         ))}
//       </div>
//     </div>
//   );
// };
import React from 'react';
import { Check } from 'lucide-react';
import { useSiteBuilder } from 'src/context/SiteBuilderContext';

interface StepIndicatorProps {
  currentStep: number;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep }) => {
  const { goToStep } = useSiteBuilder();

  const steps = [
    { number: 1, title: 'Website Info' },
    { number: 2, title: 'Description' },
    { number: 3, title: 'Contact Info' },
    { number: 4, title: 'Images' },
    { number: 5, title: 'Templates' },
    { number: 6, title: 'Customize' },
    { number: 7, title: 'Generate' },
  ];

  return (
    <div className="w-full px-2">
      <div className="grid grid-cols-7 gap-1 sm:flex sm:items-center sm:justify-between">
        {steps.map((step) => (
          <div
            key={step.number}
            className="flex flex-col items-center text-center cursor-pointer"
            onClick={() => {
              if (step.number <= currentStep) {
                goToStep(step.number);
              }
            }}
          >
            <div
              className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full transition-all duration-300 text-sm sm:text-base ${
                step.number < currentStep
                  ? 'bg-[#7DBE1D] text-white'
                  : step.number === currentStep
                  ? 'bg-white border-2 border-[#7DBE1D] ring-2 sm:ring-4 ring-[#C5DB9D]/30 text-[#6D6D81]'
                  : 'bg-gray-200 text-gray-500'
              }`}
            >
              {step.number < currentStep ? <Check className="w-4 h-4 sm:w-5 sm:h-5" /> : step.number}
            </div>
            <div
              className={`hidden sm:block mt-1 text-[10px] sm:text-xs transition-all duration-300 ${
                step.number <= currentStep ? 'text-[#7DBE1D] font-medium' : 'text-gray-500'
              }`}
            >
              {step.title}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
