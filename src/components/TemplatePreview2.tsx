// import React from 'react';

// interface TemplatePreviewProps {
//   //postId: number;
//   sitePath: string; // full URL, e.g., "http://localhost/design-001"
// }

// const TemplatePreview: React.FC<TemplatePreviewProps> = ({ sitePath }) => {
//   return (
//     <iframe
//       src={`${sitePath}&_cache=${Date.now()}`}
//       style={{ width: '100%', height: '100vh', border: 'none' }}
//       title="Template Preview"
//     />
//   );
// };

// export default TemplatePreview;



// import React from 'react';

// interface TemplatePreviewProps {
//   sitePath: string;
// }

// const TemplatePreview: React.FC<TemplatePreviewProps> = ({ sitePath }) => {
//   const url = new URL(sitePath);
//   const searchParams = url.searchParams;

//   // Replace static values here with real data if needed
//   const heroImage = searchParams.get('heroImageUrl') || '';
//   const headerImage = searchParams.get('headerImageUrl') || '';

//   // Cache-busting param
//   searchParams.set('_cache', Date.now().toString());

//   const previewUrl = `${sitePath}${sitePath.includes('?') ? '&' : '?'}heroImageUrl=${encodeURIComponent(heroImage)}&headerImageUrl=${encodeURIComponent(headerImage)}&_cache=${Date.now()}`;

//   return (
//     <iframe
//       src={previewUrl}
//       style={{ width: '100%', height: '100vh', border: 'none' }}
//       title="Template Preview"
//     />
//   );
// };

// export default TemplatePreview;

// //working version
// import React from 'react';

// interface TemplatePreviewProps {
//   sitePath: string;
// }

// const TemplatePreview: React.FC<TemplatePreviewProps> = ({ sitePath }) => {
//   const url = new URL(sitePath, window.location.origin); // 👈 fixed
//   const searchParams = url.searchParams;

//   const heroImage = searchParams.get('heroImageUrl') || '';
//   const headerImage = searchParams.get('headerImageUrl') || '';

//   searchParams.set('_cache', Date.now().toString());

//   const previewUrl = url.toString();

//   return (
//     <iframe
//       src={previewUrl}
//       style={{ width: '100%', height: '100vh', border: 'none' }}
//       title="Template Preview"
//     />
//   );
// };

// export default TemplatePreview;


//testing toggle

import React from 'react';

interface TemplatePreviewProps {
  sitePath: string;
  device: 'desktop' | 'tablet' | 'mobile';
}

const DEVICE_WIDTHS = {
  desktop: '100%',
  tablet: '768px',
  mobile: '375px',
};

const DEVICE_HEIGHTS = {
  desktop: '100%',
  tablet: '1024px',
  mobile: '667px',
};

const TemplatePreview: React.FC<TemplatePreviewProps> = ({ sitePath, device }) => {
  const url = new URL(sitePath, window.location.origin);
  url.searchParams.set('_cache', Date.now().toString());
  const previewUrl = url.toString();

  return (
    <iframe
      src={previewUrl}
      title="Template Preview"
      style={{
        width: DEVICE_WIDTHS[device],
        height: DEVICE_HEIGHTS[device],
        border: '1px solid #ccc',
        borderRadius: '8px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
      }}
    />
  );
};

export default TemplatePreview;

