'use client';

import { useSiteBuilder } from '@/context/SiteBuilderContext';

export default function GlobalLoader() {
  const { isGlobalLoading } = useSiteBuilder();

  if (!isGlobalLoading) return null;

  return (
    <div className="fixed inset-0 z-50 bg-white/60 backdrop-blur-sm flex items-center justify-center">
      <div className="w-10 h-10 border-4 border-gray-300 border-t-[#7DBE1D] rounded-full animate-spin" />
    </div>
  );
}
