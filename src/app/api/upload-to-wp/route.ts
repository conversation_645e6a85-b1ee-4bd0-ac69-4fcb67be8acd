// // src/app/api/upload-to-wp/route.ts

// import { NextRequest, NextResponse } from 'next/server';

// export async function POST(req: NextRequest) {
//   const formData = await req.formData();
//   const file = formData.get('file') as Blob;

//   if (!file) {
//     return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
//   }

//   const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;

//   const wpFormData = new FormData();
//   wpFormData.append('file', file, 'upload.jpg');

//   const username = process.env.WP_ADMIN_USER!;
//   const appPassword = process.env.WP_APP_PASSWORD!;
//   const credentials = Buffer.from(`${username}:${appPassword}`).toString('base64');
//   console.log('Using credentials:', credentials);

//   const wpRes = await fetch(`${BASE_URL}/wp-json/wp/v2/media`, {
//     method: 'POST',
//     headers: {
//       Authorization: `Basic ${credentials}`,
//       'Content-Disposition': 'attachment; filename="upload.jpg"',
//     },
//     body: wpFormData,
//   });

//   const wpJson = await wpRes.json();

//   if (!wpRes.ok) {
//     return NextResponse.json({ error: wpJson.message || 'Failed to upload to WordPress' }, { status: wpRes.status });
//   }

//   return NextResponse.json({
//     url: wpJson.source_url,
//     id: wpJson.id, // add this
//   });
// }




//for multisite network

import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const file = formData.get('file') as Blob;

  if (!file) {
    return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
  }

  const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;
  
  // Create site-specific URL for multisite
  const uploadUrl = `${BASE_URL}/wp-json/wp/v2/media`;
  
  const wpFormData = new FormData();
  wpFormData.append('file', file, 'upload.jpg');

  const username = process.env.WP_ADMIN_USER!;
  const appPassword = process.env.WP_APP_PASSWORD!;
  const credentials = Buffer.from(`${username}:${appPassword}`).toString('base64');

  try {
    const wpRes = await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${credentials}`,
        // Remove Content-Disposition - let browser set it automatically
      },
      body: wpFormData,
    });

    if (!wpRes.ok) {
      const errorText = await wpRes.text();
      console.error('WordPress upload error:', wpRes.status, errorText);
      return NextResponse.json({ error: `Upload failed: ${errorText}` }, { status: wpRes.status });
    }

    const wpJson = await wpRes.json();
    return NextResponse.json({
      url: wpJson.source_url,
      id: wpJson.id,
    });
    
  } catch (error) {
    console.error('Network error:', error);
    return NextResponse.json({ error: 'Network error during upload' }, { status: 500 });
  }
}