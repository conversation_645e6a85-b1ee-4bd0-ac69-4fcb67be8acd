import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });

export async function POST(req: Request) {
  const { siteName, category, brief } = await req.json();

  const prompt = `Generate 5 realistic testimonials for ${siteName}, a ${category} website.
Brief: ${brief}

Each testimonial should include:
- Customer name (realistic, diverse)
- Rating (1-5 stars)
- Short testimonial text (1-3 sentences)
- Date (within the last 6 months)

Return a JSON array.`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    });

    const testimonials = JSON.parse(response.choices[0].message.content || '[]');
    return NextResponse.json({ testimonials });
  } catch (error) {
    console.error('Error generating testimonials:', error);
    return NextResponse.json({ error: 'Failed to generate testimonials' }, { status: 500 });
  }
}
