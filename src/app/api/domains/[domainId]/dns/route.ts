import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

// GET /api/domains/[domainId]/dns - Fetch DNS records for a domain
export async function GET(
  request: NextRequest,
  { params }: { params: { domainId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId } = params;

    if (!domainId) {
      return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
    }

    // First, verify the user owns this domain
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    // Fetch DNS records for the domain
    const { data: records, error } = await supabaseAdmin
      .from('dns_records')
      .select('*')
      .eq('domain_id', domainId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching DNS records:', error);
      return NextResponse.json({ error: 'Failed to fetch DNS records' }, { status: 500 });
    }

    return NextResponse.json({ records: records || [] });
  } catch (error: any) {
    console.error('DNS records API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/domains/[domainId]/dns - Create a new DNS record
export async function POST(
  request: NextRequest,
  { params }: { params: { domainId: string } }
) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { domainId } = params;

    if (!domainId) {
      return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
    }

    // First, verify the user owns this domain
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id')
      .eq('id', domainId)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    const body = await request.json();
    const {
      type,
      name,
      value,
      ttl = 3600,
      priority,
      status = 'pending'
    } = body;

    // Validate required fields
    if (!type || !name || !value) {
      return NextResponse.json({ error: 'Type, name, and value are required' }, { status: 400 });
    }

    // Validate DNS record type
    const validTypes = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SRV', 'CAA'];
    if (!validTypes.includes(type)) {
      return NextResponse.json({ error: 'Invalid DNS record type' }, { status: 400 });
    }

    // Validate TTL
    if (ttl < 60 || ttl > 604800) {
      return NextResponse.json({ error: 'TTL must be between 60 and 604800 seconds' }, { status: 400 });
    }

    // Validate priority for MX records
    if (type === 'MX' && (priority === undefined || priority < 0 || priority > 65535)) {
      return NextResponse.json({ error: 'Priority is required for MX records and must be between 0 and 65535' }, { status: 400 });
    }

    // Create the DNS record
    const { data: record, error } = await supabaseAdmin
      .from('dns_records')
      .insert({
        domain_id: domainId,
        type,
        name: name.trim(),
        value: value.trim(),
        ttl,
        priority: type === 'MX' ? priority : null,
        status
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating DNS record:', error);
      return NextResponse.json({ error: 'Failed to create DNS record' }, { status: 500 });
    }

    return NextResponse.json({ record });
  } catch (error: any) {
    console.error('DNS record creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 