import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: Request) {
  try {
    const { brief } = await req.json();
    
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{
        role: "user",
        content: `Improve this website brief while maintaining its core message. Make it more professional and compelling. 
        Return only the improved text without any additional formatting. Here's the original: ${brief}`
      }],
      temperature: 0.7,
      max_tokens: 1000
    });

    return NextResponse.json({ 
      improvedBrief: completion.choices[0].message.content 
    });
    
  } catch (error) {
    console.error('OpenAI API error:', error);
    return NextResponse.json(
      { error: "Failed to improve brief" },
      { status: 500 }
    );
  }
}