//this is for handling the ACF hero image only
// import { NextRequest, NextResponse } from 'next/server';

// export async function POST(req: NextRequest) {
//   const { imageId } = await req.json();

//   if (!imageId) {
//     return NextResponse.json({ error: 'No image ID provided' }, { status: 400 });
//   }

//   const username = 'admin'; // 🔒 replace with yours
//   const appPassword = 'Dhee 8LQ2 lI4t 0MEh p4ZD QCTZ'; // 🔒 replace with yours
//   const credentials = Buffer.from(`${username}:${appPassword}`).toString('base64');

//   // Get front page ID
//   const settingsRes = await fetch('http://localhost/wp-json/wp/v2/settings', {
//     headers: {
//       Authorization: `Basic ${credentials}`,
//     },
//   });
//   const settings = await settingsRes.json();
//   const frontPageId = settings.page_on_front;

//   // Update ACF field
//   const updateRes = await fetch(`http://localhost/wp-json/acf/v3/pages/${frontPageId}`, {
//     method: 'POST',
//     headers: {
//       Authorization: `Basic ${credentials}`,
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({
//       fields: {
//         hero_image: imageId,
//       },
//     }),
//   });

//   const updateJson = await updateRes.json();

//   if (!updateRes.ok) {
//     return NextResponse.json({ error: updateJson.message || 'Failed to update ACF' }, { status: updateRes.status });
//   }

//   return NextResponse.json({ success: true });
// }


//this is to handle the ACF hero image and big title image both
//for not multisite network
// import { NextRequest, NextResponse } from 'next/server';

// export async function POST(req: NextRequest) {
//   const { heroImageId, bigTitleImageId } = await req.json();

//   if (!heroImageId && !bigTitleImageId) {
//     return NextResponse.json({ error: 'No image ID(s) provided' }, { status: 400 });
//   }

//   const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;

//   const username = process.env.WP_ADMIN_USER!;
//   const appPassword = process.env.WP_APP_PASSWORD!;
//   const credentials = Buffer.from(`${username}:${appPassword}`).toString('base64');

//   const settingsRes = await fetch(`${BASE_URL}/wp-json/wp/v2/settings`, {
//     headers: {
//       Authorization: `Basic ${credentials}`,
//     },
//   });

//   const settings = await settingsRes.json();
//   const frontPageId = settings.page_on_front;

//   const fields: any = {};
//   if (heroImageId) fields.hero_image = heroImageId;
//   if (bigTitleImageId) fields.big_title_background = bigTitleImageId;

//   const updateRes = await fetch(`${BASE_URL}/wp-json/acf/v3/pages/${frontPageId}`, {
//     method: 'POST',
//     headers: {
//       Authorization: `Basic ${credentials}`,
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({ fields }),
//   });

//   const updateJson = await updateRes.json();

//   if (!updateRes.ok) {
//     return NextResponse.json({ error: updateJson.message || 'Failed to update ACF' }, { status: updateRes.status });
//   }

//   return NextResponse.json({ success: true });
// }




//for multisite network

import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const { heroImageId, bigTitleImageId } = await req.json();

  if (!heroImageId && !bigTitleImageId) {
    return NextResponse.json({ error: 'No image ID(s) provided' }, { status: 400 });
  }

  const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;
  const username = process.env.WP_ADMIN_USER!;
  const appPassword = process.env.WP_APP_PASSWORD!;
  const credentials = Buffer.from(`${username}:${appPassword}`).toString('base64');

  try {
    // Get site settings
    const settingsRes = await fetch(`${BASE_URL}/wp-json/wp/v2/settings`, {
      headers: { Authorization: `Basic ${credentials}` },
    });

    if (!settingsRes.ok) {
      const errorText = await settingsRes.text();
      console.error('Settings fetch error:', settingsRes.status, errorText);
      return NextResponse.json({ error: `Failed to get settings: ${errorText}` }, { status: settingsRes.status });
    }

    const settings = await settingsRes.json();
    const frontPageId = settings.page_on_front;

    const fields: any = {};
    if (heroImageId) fields.hero_image = heroImageId;
    if (bigTitleImageId) fields.big_title_background = bigTitleImageId;

    // Update ACF fields
    const updateRes = await fetch(`${BASE_URL}/wp-json/acf/v3/pages/${frontPageId}`, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${credentials}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fields }),
    });

    if (!updateRes.ok) {
      const errorText = await updateRes.text();
      console.error('ACF update error:', updateRes.status, errorText);
      return NextResponse.json({ error: `ACF update failed: ${errorText}` }, { status: updateRes.status });
    }

    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Network error:', error);
    return NextResponse.json({ error: 'Network error' }, { status: 500 });
  }
}