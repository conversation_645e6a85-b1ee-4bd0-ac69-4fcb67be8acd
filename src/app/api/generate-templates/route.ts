// import { NextResponse } from 'next/server';
// import OpenAI from 'openai';

// const openai = new OpenAI({
//   apiKey: process.env.OPENAI_API_KEY,
// });

// export async function POST(req: Request) {
//   const { siteName, category, brief, images, contact } = await req.json();

//   try {
//     // Step 1: Generate template concepts
//     const conceptsPrompt = `Generate 3 distinct website template concepts for ${siteName} (${category}).
//     Brief: ${brief}
//     Output JSON with: name, description, colorScheme (including primary, secondary, accent, background, text colors), typography (heading and body fonts)`;
    
//     const conceptsResponse = await openai.chat.completions.create({
//       model: 'gpt-4',
//       messages: [{ role: 'user', content: conceptsPrompt }],
//       temperature: 0.7,
//     });

//     const basicConcepts = JSON.parse(conceptsResponse.choices[0].message.content || '[]');

//     // Step 2: Generate page content for each template
//     const fullTemplates = await Promise.all(basicConcepts.map(async (concept, index) => {
//       // Generate content for each page
//       const pagesPrompt = `Create website content for ${siteName} (${category}) with style: ${concept.description}.
//       Brief: ${brief}
//       Contact: ${JSON.stringify(contact)}
      
//       Create content for these pages: Home, About, Services, Gallery, Blog, Testimonials, Contact.
      
//       For each page, create appropriate sections. For example:
//       - Home: hero, features, testimonials, call-to-action
//       - About: team, mission, values
//       - Services: service list, benefits, pricing
//       - etc.
      
//       Output as structured JSON with content for each section.`;
      
//       const pagesResponse = await openai.chat.completions.create({
//         model: 'gpt-4',
//         messages: [{ role: 'user', content: pagesPrompt }],
//         temperature: 0.7,
//       });
      
//       const pages = JSON.parse(pagesResponse.choices[0].message.content || '{}');
      
//       // Generate thumbnail
//       const imageResponse = await openai.images.generate({
//         prompt: `Professional website thumbnail for ${concept.name}: ${concept.description}. Clean, modern web design in ${concept.colorScheme.primary} and ${concept.colorScheme.secondary} colors. Photorealistic rendering, 8K detail.`,
//         n: 1,
//         size: '512x512',
//       });

//       return {
//         id: `template-${index + 1}`,
//         name: concept.name,
//         description: concept.description,
//         thumbnail: imageResponse.data[0]?.url || '',
//         colorScheme: concept.colorScheme,
//         typography: concept.typography,
//         pages: pages
//       };
//     }));

//     return NextResponse.json({ templates: fullTemplates });
//   } catch (error) {
//     console.error('Template generation error:', error);
//     return NextResponse.json(
//       { error: 'Failed to generate templates' },
//       { status: 500 }
//     );
//   }
// }


// import { NextResponse } from 'next/server';
// import OpenAI from 'openai';

// const openai = new OpenAI({
//   apiKey: process.env.OPENAI_API_KEY,
// });

// // Map site categories to default page lists
// const categoryPagesMap: Record<string, string[]> = {
//   Business:   ['Home', 'About', 'Services', 'Blog', 'Contact'],
//   Portfolio:  ['Home', 'About', 'Gallery', 'Blog', 'Contact'],
//   Restaurant: ['Home', 'Menu', 'Offerings', 'Gallery', 'Contact'],
//   Ecommerce:  ['Home', 'Products', 'Offers', 'Blog', 'Contact'],
//   Nonprofit:  ['Home', 'About', 'Our Impact', 'Blog', 'Contact'],
//   Personal:   ['Home', 'About', 'Blog', 'Contact'],
//   Other:      ['Home', 'About', 'Services', 'Blog', 'Contact'],
// };

// export async function POST(req: Request) {
//   console.time('TemplateGen');

//   const {
//     siteName,
//     category,
//     brief,
//     contact,
//     selectedImages = [],
//     uploadedImages = [],
//     templateCount = 1,

//   } = await req.json();

//   // Determine pages for this category
//   const pageList = categoryPagesMap[category] || ['Home', 'About', 'Menus', 'Services', 'Gallery', 'Blog', 'Contact'];

//   // Prepare payload
//   const templates = await Promise.all(
//   Array.from({ length: templateCount }).map(async (_, variantIndex) => {
//     // For each variant, build its pages
//     const pages = await Promise.all(
//       pageList.map(async (pageName) => {
//         // 1) Generate the hero text
//         const textPrompt = `
//           [Template #${variantIndex + 1}] Write the "${pageName}" page content
//           for "${siteName}" (${category}). Brief: ${brief}
//           Include headings, an intro, and a clear CTA.
//         `.trim();
//         const textRes = await openai.chat.completions.create({
//           model: 'gpt-4',
//           messages: [{ role: 'user', content: textPrompt }],
//           temperature: 0.7,
//         });
//         const pageText = textRes.choices[0]?.message?.content?.trim() || '';

//         // 2) Stitch together up to 3 user images
//         const userImgs = [...selectedImages, ...uploadedImages]
//           .slice(0, 3)
//           .map(url => ({ source: 'user' as const, url }));

//         // 3) Generate 2 AI images
//         const imgPrompt = `
//           [Template #${variantIndex + 1}] Generate a hero image for "${pageName}"
//           of "${siteName}" (${category}). Tone: ${brief.slice(0, 200)}.
//           Style: modern, clean.
//         `.trim();
//         const imgRes = await openai.images.generate({
//           prompt: imgPrompt,
//           n: 2,
//           size: '1024x1024',
//         });
//         const aiImgs = imgRes.data.map(d => ({
//           source: 'generated' as const,
//           url: d.url
//         }));

//         return { pageName, text: pageText, images: [...userImgs, ...aiImgs] };
//       })
//     );

//     return { siteName, category, brief, contact, pages };
//   })
// );

//   // Finally send back both templates
//   return NextResponse.json({ templates });
// }


import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Map site categories to default page lists
const categoryPagesMap: Record<string, string[]> = {
  Business:   ['Home', 'About', 'Services', 'Blog', 'Contact'],
  Portfolio:  ['Home', 'About', 'Gallery', 'Blog', 'Contact'],
  Restaurant: ['Home', 'Menu', 'Offerings', 'Gallery', 'Contact'],
  Ecommerce:  ['Home', 'Products', 'Offers', 'Blog', 'Contact'],
  Nonprofit:  ['Home', 'About', 'Our Impact', 'Blog', 'Contact'],
  Personal:   ['Home', 'About', 'Blog', 'Contact'],
  Other:      ['Home', 'About', 'Services', 'Blog', 'Contact'],
};

// Retry helper for rate limits
async function retryRequest<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
  try {
    return await fn();
  } catch (err: any) {
    if (retries <= 0 || err.status !== 429) throw err;
    console.warn(`Rate limit hit. Retrying in ${delay}ms...`);
    await new Promise(res => setTimeout(res, delay));
    return retryRequest(fn, retries - 1, delay * 2);
  }
}

export async function POST(req: Request) {
  const {
    siteName,
    category,
    brief,
    contact,
    selectedImages = [],
    uploadedImages = [],
  } = await req.json();

  console.log('Received POST request with the following data:', {
    siteName,
    category,
    brief,
    contact,
    selectedImages,
    uploadedImages,
  });

  const templateNumber = 1;
  const pageList = categoryPagesMap[category] || ['Home', 'About', 'Blog', 'Contact'];
  const stylePrompt = "Modern minimalist design with clean typography";

  const pages = [];

  for (const pageName of pageList) {
    console.log(`Generating content for page: ${pageName}`);

    const textPrompt = `
      Create a modern version of the "${pageName}" page for:
      Website Name: ${siteName}
      Category: ${category}
      Description: ${brief}

      Style Guidelines: ${stylePrompt}
      Include 3-4 sections with appropriate headings and content.
    `;
    console.log(`Text prompt for "${pageName}":`, textPrompt.trim());

    const textRes = await retryRequest(() =>
      openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: textPrompt.trim() }],
        temperature: 0.8,
      })
    );
    const generatedText = textRes.choices[0]?.message?.content?.trim() || '';
    console.log(`Generated text for "${pageName}":`, generatedText);

    const imgPrompt = `
      Modern minimalist hero image for ${pageName} page.
      Site: ${siteName} (${category})
      Key elements: ${brief.substring(0, 200)}
      Style: Clean lines, muted colors
    `;
    console.log(`Image prompt for "${pageName}":`, imgPrompt.trim());

    const imgRes = await retryRequest(() =>
      openai.images.generate({
        prompt: imgPrompt.trim(),
        n: 2,
        size: '1024x1024',
      })
    );

    const generatedImages = imgRes.data.map(d => ({ source: 'generated', url: d.url }));
    console.log(`Generated image URLs for "${pageName}":`, generatedImages.map(img => img.url));

    const userImgs = [...selectedImages, ...uploadedImages]
      .slice(0, 2)
      .map(url => ({ source: 'user', url }));

    const pageData = {
      pageName,
      text: generatedText,
      images: [...userImgs, ...generatedImages],
    };

    console.log(`Final composed data for "${pageName}":`, pageData);
    pages.push(pageData);
  }

  const template = {
    name: `${siteName} Template ${templateNumber}`,
    description: 'Modern Clean Layout',
    pages,
    style: {
      colors: {
        primary: '#2d3748',
        secondary: '#4a5568',
        accent: '#4299e1',
        background: '#ffffff',
        text: '#1a202c',
      },
    },
  };

  console.log('Final generated template:', template);

  return NextResponse.json([template]);
}