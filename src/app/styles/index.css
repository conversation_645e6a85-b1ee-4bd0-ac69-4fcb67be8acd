@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fadeInUp 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #7DBE1D, #C5DB9D);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #5E9A12, #B5D27E);
}

/* Hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

/* Glass morphism effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Gradient text */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* Button hover effects */
.group:hover .absolute {
  opacity: 0.75;
}

/* Parallax effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Loading states */
.loading {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .text-7xl {
    font-size: 3.5rem;
  }
  
  .text-6xl {
    font-size: 2.5rem;
  }
  
  .text-5xl {
    font-size: 2rem;
  }
}

/* Focus states for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid #8B5CF6;
  outline-offset: 2px;
}

/* Improved contrast for better accessibility */
.text-gray-400 {
  color: #9CA3AF;
}

.text-gray-600 {
  color: #6B7280;
}

/* Animation delays */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-1000 {
  animation-delay: 1s;
}