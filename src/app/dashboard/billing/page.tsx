'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../components/providers/AuthProvider';
import { createClient } from '@supabase/supabase-js';
import { AlertCircle, CreditCard, Calendar, CheckCircle, XCircle, Clock, Loader2, RefreshCw, Download, X, ExternalLink } from 'lucide-react';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// TypeScript interfaces for site billing data
interface SiteBillingData {
  id: string;
  site_name: string;
  subscription_status: 'active' | 'inactive' | 'pending' | 'cancelled' | 'expired';
  plan_name: string;
  amount: number;
  currency: string;
  next_billing_date: string | null;
  created_at: string;
  updated_at: string;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  site_id: string;
  user_id: string;
  last_invoice_amount?: number;
  last_invoice_date?: string;
  last_invoice_status?: string;
}

interface SiteBillingResponse {
  siteBillingData: SiteBillingData[];
  totalSites: number;
  activeSubscriptions: number;
}

interface PaymentMethod {
  id: string;
  type: string;
  last4: string | null;
  brand: string | null;
  isDefault: boolean;
}

interface Invoice {
  id: string;
  date: string;
  amount: number;
  status: string;
  url: string | null;
}

interface PaymentDetails {
  paymentMethods: PaymentMethod[];
  invoices: Invoice[];
}

interface ApiError {
  message: string;
  status?: number;
}

export default function BillingPage() {
  const { profile, loading: authLoading } = useAuth();
  const [billingData, setBillingData] = useState<SiteBillingResponse | null>(null);
  const [billingLoading, setBillingLoading] = useState(true);
  const [error, setError] = useState<ApiError | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [selectedSite, setSelectedSite] = useState<SiteBillingData | null>(null);
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const [paymentDetailsLoading, setPaymentDetailsLoading] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  useEffect(() => {
    if (!authLoading && !profile) {
      window.location.href = '/login';
    }
  }, [profile, authLoading]);

  const fetchBillingData = async () => {
    try {
      setBillingLoading(true);
      setError(null);

      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/stripe/site-billing-data', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to fetch billing data' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data: SiteBillingResponse = await response.json();
      setBillingData(data);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch billing data';
      setError({ message: errorMessage, status: err.status });
      console.error('Billing data fetch error:', err);
    } finally {
      setBillingLoading(false);
    }
  };

  const fetchPaymentDetails = async () => {
    if (!selectedSite) return;

    try {
      setPaymentDetailsLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/stripe/billing-data', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment details');
      }

      const data = await response.json();
      setPaymentDetails({
        paymentMethods: data.paymentMethods || [],
        invoices: data.invoices || []
      });
    } catch (err: any) {
      console.error('Error fetching payment details:', err);
    } finally {
      setPaymentDetailsLoading(false);
    }
  };

  const handlePaymentDetailsClick = async (site: SiteBillingData) => {
    setSelectedSite(site);
    setIsPaymentModalOpen(true);
    await fetchPaymentDetails();
  };

  const handleInvoiceDownload = async (invoiceUrl: string) => {
    try {
      const response = await fetch(invoiceUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${Date.now()}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading invoice:', error);
      // Fallback to opening in new tab
      window.open(invoiceUrl, '_blank');
    }
  };

  useEffect(() => {
    if (!authLoading && profile) {
      fetchBillingData();
    }
  }, [authLoading, profile, retryCount]);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  const getStatusIcon = (status: SiteBillingData['subscription_status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'cancelled':
      case 'expired':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'inactive':
        return <XCircle className="w-5 h-5 text-gray-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: SiteBillingData['subscription_status']) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'pending':
        return 'Pending';
      case 'cancelled':
        return 'Cancelled';
      case 'expired':
        return 'Expired';
      case 'inactive':
        return 'Inactive';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: SiteBillingData['subscription_status']) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'cancelled':
      case 'expired':
        return 'text-red-600 bg-red-50';
      case 'inactive':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatPrice = (amount: number, currency: string) => {
    return `${currency.toUpperCase()} $${amount.toFixed(2)}`;
  };

  const isExpiringSoon = (nextBillingDate: string | null) => {
    if (!nextBillingDate) return false;
    const billing = new Date(nextBillingDate);
    const now = new Date();
    const daysUntilBilling = Math.ceil((billing.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilBilling <= 7 && daysUntilBilling > 0;
  };

  // Loading state component
  const LoadingState = () => (
    <div className="flex items-center justify-center py-12">
      <Loader2 className="w-8 h-8 mr-3 text-gray-400 animate-spin" />
      <span className="text-gray-600">Loading your billing information...</span>
    </div>
  );

  // Error state component
  const ErrorState = ({ error, onRetry }: { error: ApiError; onRetry: () => void }) => (
    <div className="p-6 border border-red-200 rounded-lg bg-red-50">
      <div className="flex items-center mb-2">
        <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
        <span className="font-medium text-red-700">Error Loading Billing Data</span>
      </div>
      <p className="mb-4 text-sm text-red-600">{error.message}</p>
      <button
        onClick={onRetry}
        className="px-4 py-2 text-white transition-colors bg-red-600 rounded-md hover:bg-red-700"
      >
        Try Again
      </button>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="py-12 text-center">
      <div className="max-w-md p-8 mx-auto rounded-lg bg-gray-50">
        <CreditCard className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="mb-2 text-xl font-semibold text-gray-600">No Billing Data Yet</h3>
        <p className="mb-6 text-gray-500">
          You haven&apos;t created any websites with billing yet. Start by creating your first website.
        </p>
      </div>
    </div>
  );

  // Payment Details Modal
  const PaymentDetailsModal = () => {
    if (!isPaymentModalOpen || !selectedSite) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={() => setIsPaymentModalOpen(false)}
        />
        
        {/* Modal */}
        <div className="relative w-full max-w-2xl mx-4 bg-white rounded-lg shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Payment Details</h3>
              <p className="text-sm text-gray-600">{selectedSite.site_name}</p>
            </div>
            <button
              onClick={() => setIsPaymentModalOpen(false)}
              className="p-2 text-gray-400 transition-colors rounded-md hover:text-gray-600 hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {paymentDetailsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 mr-2 text-gray-400 animate-spin" />
                <span className="text-gray-600">Loading payment details...</span>
              </div>
            ) : paymentDetails ? (
              <div className="space-y-6">
                {/* Payment Methods */}
                <div>
                  <h4 className="mb-3 text-sm font-medium text-gray-900">Payment Methods</h4>
                  {paymentDetails.paymentMethods.length > 0 ? (
                    <div className="space-y-2">
                      {paymentDetails.paymentMethods.map((pm) => (
                        <div key={pm.id} className="p-3 border border-gray-200 rounded-md">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <CreditCard className="w-4 h-4 text-gray-500" />
                              <span className="text-sm font-medium">
                                {pm.brand} ending in {pm.last4}
                              </span>
                              {pm.isDefault && (
                                <span className="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
                                  Default
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No payment methods found.</p>
                  )}
                </div>

                {/* Invoices */}
                <div>
                  <h4 className="mb-3 text-sm font-medium text-gray-900">Recent Invoices</h4>
                  {paymentDetails.invoices.length > 0 ? (
                    <div className="space-y-2">
                      {paymentDetails.invoices.slice(0, 5).map((invoice) => (
                        <div key={invoice.id} className="p-3 border border-gray-200 rounded-md">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div>
                                <p className="text-sm font-medium">{formatDate(invoice.date)}</p>
                                <p className="text-xs text-gray-500">{formatPrice(invoice.amount, 'usd')}</p>
                              </div>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                invoice.status === 'paid' 
                                  ? 'text-green-800 bg-green-100' 
                                  : 'text-yellow-800 bg-yellow-100'
                              }`}>
                                {invoice.status}
                              </span>
                            </div>
                            {invoice.url && (
                              <button
                                onClick={() => handleInvoiceDownload(invoice.url!)}
                                className="inline-flex items-center px-2 py-1 text-xs text-blue-600 transition-colors border border-blue-200 rounded hover:bg-blue-50"
                              >
                                <Download className="w-3 h-3 mr-1" />
                                Download
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No invoices found.</p>
                  )}
                </div>

                {/* Subscription Info */}
                <div>
                  <h4 className="mb-3 text-sm font-medium text-gray-900">Subscription Information</h4>
                  <div className="p-3 border border-gray-200 rounded-md">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Subscription ID:</span>
                        <p className="font-medium">{selectedSite.stripe_subscription_id || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Plan:</span>
                        <p className="font-medium">{selectedSite.plan_name}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="font-medium">{formatPrice(selectedSite.amount, selectedSite.currency)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Next Billing:</span>
                        <p className="font-medium">{formatDate(selectedSite.next_billing_date)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-500">Unable to load payment details.</p>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (authLoading || !profile) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <Loader2 className="w-8 h-8 text-indigo-600 animate-spin" />
        <p className="text-gray-600">Checking authentication...</p>
      </div>
    );
  }

  if (billingLoading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={handleRetry} />;
  }

  if (!billingData || billingData.siteBillingData.length === 0) {
    return <EmptyState />;
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">My Billing</h2>
            <p className="text-gray-600">Manage your website subscriptions and billing information</p>
          </div>
          <button
            onClick={fetchBillingData}
            className="px-4 py-2 text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <RefreshCw className="inline w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>

        {/* Billing Summary */}
        <div className="grid gap-4 mb-6 sm:grid-cols-3">
          <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="text-sm font-medium text-gray-500">Total Sites</div>
            <div className="text-2xl font-bold text-gray-900">{billingData.totalSites}</div>
          </div>
          <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="text-sm font-medium text-gray-500">Active Subscriptions</div>
            <div className="text-2xl font-bold text-green-600">{billingData.activeSubscriptions}</div>
          </div>
          <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
            <div className="text-sm font-medium text-gray-500">Total Monthly</div>
            <div className="text-2xl font-bold text-gray-900">
              ${billingData.siteBillingData
                .filter(site => site.subscription_status === 'active')
                .reduce((sum, site) => sum + site.amount, 0)
                .toFixed(2)}
            </div>
          </div>
        </div>

        {/* Billing Cards Grid */}
        <div className="grid gap-6">
          {billingData.siteBillingData.map((site) => (
            <div key={site.id} className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                {/* Site Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center mb-2 space-x-3">
                    <h3 className="text-xl font-semibold text-gray-800 truncate">
                      {site.site_name}
                    </h3>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(site.subscription_status)}
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(site.subscription_status)}`}>
                        {getStatusText(site.subscription_status)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-3 text-sm sm:grid-cols-2 lg:grid-cols-4">
                    <div>
                      <span className="text-gray-500">Plan:</span>
                      <p className="font-medium">{site.plan_name}</p>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">Amount:</span>
                      <p className="font-medium">{formatPrice(site.amount, site.currency)}</p>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">Next Billing:</span>
                      <p className={`font-medium ${isExpiringSoon(site.next_billing_date) ? 'text-amber-600' : ''}`}>
                        {formatDate(site.next_billing_date)}
                        {isExpiringSoon(site.next_billing_date) && (
                          <span className="ml-1 text-amber-600">⚠️</span>
                        )}
                      </p>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">Last Invoice:</span>
                      <p className="font-medium">
                        {site.last_invoice_amount ? (
                          <span className="text-green-600">{formatPrice(site.last_invoice_amount, site.currency)}</span>
                        ) : (
                          <span className="text-gray-400">No invoices</span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex flex-col gap-2 sm:flex-row lg:ml-6">
                  <button 
                    onClick={() => handlePaymentDetailsClick(site)}
                    className="inline-flex items-center px-3 py-2 text-sm text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <CreditCard className="w-4 h-4 mr-1" />
                    Payment Details
                  </button>
                </div>
              </div>

              {/* Additional Info */}
              <div className="pt-4 mt-4 border-t border-gray-100">
                <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500">
                  <span>Subscription ID: {site.stripe_subscription_id || 'N/A'}</span>
                  <span>Created: {formatDate(site.created_at)}</span>
                  {site.last_invoice_status && (
                    <span>Last Invoice: {site.last_invoice_status}</span>
                  )}
                </div>
              </div>

              {/* Billing Warning */}
              {isExpiringSoon(site.next_billing_date) && (
                <div className="p-3 mt-4 border rounded-md bg-amber-50 border-amber-200">
                  <div className="flex items-center">
                    <AlertCircle className="w-4 h-4 mr-2 text-amber-600" />
                    <span className="text-sm font-medium text-amber-800">
                      Next billing date is approaching! Ensure your payment method is up to date.
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Payment Details Modal */}
      <PaymentDetailsModal />
    </>
  );
}
