"use client";

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft, Plus, Loader2, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { useAuth } from '../../../../../components/providers/AuthProvider';
import DNSRecordsTable from '../../../../../components/dashboard/domain/DNSRecordsTable';
import AddDNSRecordModal from '../../../../../components/dashboard/domain/AddDNSRecordModal';
import EditDNSRecordModal from '../../../../../components/dashboard/domain/EditDNSRecordModal';
import DeleteDNSRecordModal from '../../../../../components/dashboard/domain/DeleteDNSRecordModal';

export interface DNSRecord {
  id: string;
  domain_id: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV' | 'CAA';
  name: string;
  value: string;
  ttl: number;
  priority?: number; // For MX records
  status: 'active' | 'pending' | 'error';
  created_at: string;
  updated_at: string;
}

interface Domain {
  id: string;
  domain_name: string;
  status: 'pending' | 'registered' | 'active' | 'expired' | 'failed';
  dns_configured: boolean;
}

const DNSManagementPage = () => {
  const params = useParams();
  const router = useRouter();
  const { user, session } = useAuth();
  const domainId = params.domainId as string;

  // State management
  const [domain, setDomain] = useState<Domain | null>(null);
  const [dnsRecords, setDnsRecords] = useState<DNSRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<DNSRecord | null>(null);

  useEffect(() => {
    if (user && domainId) {
      fetchDomainAndRecords();
    }
  }, [user, domainId]);

  const fetchDomainAndRecords = async () => {
    if (!user || !domainId) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch domain details
      const domainResponse = await fetch(`/api/domains/${domainId}`, {
        headers: {
          'Authorization': `Bearer ${session?.access_token || ''}`,
        },
      });

      if (!domainResponse.ok) {
        throw new Error('Failed to fetch domain details');
      }

      const domainData = await domainResponse.json();
      setDomain(domainData.domain);

      // Fetch DNS records
      const recordsResponse = await fetch(`/api/domains/${domainId}/dns`, {
        headers: {
          'Authorization': `Bearer ${session?.access_token || ''}`,
        },
      });

      if (!recordsResponse.ok) {
        throw new Error('Failed to fetch DNS records');
      }

      const recordsData = await recordsResponse.json();
      setDnsRecords(recordsData.records || []);
    } catch (err: any) {
      console.error('Error fetching domain and DNS records:', err);
      setError(err.message || 'Failed to load domain and DNS records');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDomainAndRecords();
    setRefreshing(false);
  };

  const handleAddRecord = async (record: Omit<DNSRecord, 'id' | 'domain_id' | 'created_at' | 'updated_at'>) => {
    try {
      const response = await fetch(`/api/domains/${domainId}/dns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || ''}`,
        },
        body: JSON.stringify(record),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add DNS record');
      }

      const data = await response.json();
      setDnsRecords(prev => [data.record, ...prev]);
      setShowAddModal(false);
    } catch (err: any) {
      console.error('Error adding DNS record:', err);
      setError(err.message || 'Failed to add DNS record');
    }
  };

  const handleEditRecord = (record: DNSRecord) => {
    setSelectedRecord(record);
    setShowEditModal(true);
  };

  const handleUpdateRecord = async (updatedRecord: DNSRecord) => {
    try {
      const response = await fetch(`/api/domains/${domainId}/dns/${updatedRecord.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token || ''}`,
        },
        body: JSON.stringify({
          type: updatedRecord.type,
          name: updatedRecord.name,
          value: updatedRecord.value,
          ttl: updatedRecord.ttl,
          priority: updatedRecord.priority,
          status: updatedRecord.status,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update DNS record');
      }

      const data = await response.json();
      setDnsRecords(prev => prev.map(record => 
        record.id === updatedRecord.id ? data.record : record
      ));
      setSelectedRecord(null);
      setShowEditModal(false);
    } catch (err: any) {
      console.error('Error updating DNS record:', err);
      setError(err.message || 'Failed to update DNS record');
    }
  };

  const handleDeleteRecord = (record: DNSRecord) => {
    setSelectedRecord(record);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedRecord) return;

    try {
      const response = await fetch(`/api/domains/${domainId}/dns/${selectedRecord.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session?.access_token || ''}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete DNS record');
      }

      setDnsRecords(prev => prev.filter(record => record.id !== selectedRecord.id));
      setSelectedRecord(null);
      setShowDeleteModal(false);
    } catch (err: any) {
      console.error('Error deleting DNS record:', err);
      setError(err.message || 'Failed to delete DNS record');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 mr-3 text-gray-400 animate-spin" />
            <span className="text-gray-600">Loading DNS management...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="p-6 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center mb-2">
              <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
              <span className="font-medium text-red-700">Error Loading DNS Management</span>
            </div>
            <p className="mb-4 text-sm text-red-600">{error}</p>
            <button
              onClick={fetchDomainAndRecords}
              className="px-4 py-2 text-white transition-colors bg-red-600 rounded-md hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="p-6 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center mb-2">
              <XCircle className="w-5 h-5 mr-2 text-red-500" />
              <span className="font-medium text-red-700">Domain Not Found</span>
            </div>
            <p className="mb-4 text-sm text-red-600">The requested domain could not be found or you don't have access to it.</p>
            <button
              onClick={() => router.push('/dashboard/domain')}
              className="px-4 py-2 text-white transition-colors bg-red-600 rounded-md hover:bg-red-700"
            >
              Back to Domains
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => router.push('/dashboard/domain')}
              className="flex items-center text-gray-600 transition-colors hover:text-gray-800 mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              Back to Domains
            </button>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="mb-2 text-2xl font-bold text-gray-800 sm:mb-4 sm:text-3xl lg:text-4xl">
                Manage DNS
              </h1>
              <p className="text-sm text-gray-600 sm:text-base">
                Manage DNS records for <span className="font-semibold text-gray-800">{domain.domain_name}</span>
              </p>
            </div>
            
            <div className="flex items-center space-x-3 mt-4 sm:mt-0">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center px-4 py-2 text-gray-700 transition-colors border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                {refreshing ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4 mr-2" />
                )}
                Refresh
              </button>
              
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center px-4 py-2 text-white transition-colors bg-green-600 rounded-md hover:bg-green-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Record
              </button>
            </div>
          </div>
        </div>

        {/* Domain Status */}
        <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`px-3 py-1 text-sm font-medium rounded-full ${
                domain.status === 'active' 
                  ? 'text-green-600 bg-green-50' 
                  : domain.status === 'registered'
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-yellow-600 bg-yellow-50'
              }`}>
                {domain.status.charAt(0).toUpperCase() + domain.status.slice(1)}
              </div>
              <span className="text-sm text-gray-500">
                DNS: {domain.dns_configured ? 'Configured' : 'Pending Configuration'}
              </span>
            </div>
            
            {!domain.dns_configured && (
              <div className="flex items-center text-amber-600">
                <AlertCircle className="w-4 h-4 mr-1" />
                <span className="text-sm font-medium">DNS configuration required</span>
              </div>
            )}
          </div>
        </div>

        {/* DNS Records Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <DNSRecordsTable
            records={dnsRecords}
            domainName={domain.domain_name}
            onEdit={handleEditRecord}
            onDelete={handleDeleteRecord}
            loading={refreshing}
          />
        </div>

        {/* Modals */}
        <AddDNSRecordModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onAdd={handleAddRecord}
          domainId={domainId}
          domainName={domain.domain_name}
        />

        <EditDNSRecordModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedRecord(null);
          }}
          onUpdate={handleUpdateRecord}
          record={selectedRecord}
          domainName={domain.domain_name}
        />

        <DeleteDNSRecordModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedRecord(null);
          }}
          onConfirm={handleConfirmDelete}
          record={selectedRecord}
          domainName={domain.domain_name}
        />
      </div>
    </div>
  );
};

export default DNSManagementPage; 