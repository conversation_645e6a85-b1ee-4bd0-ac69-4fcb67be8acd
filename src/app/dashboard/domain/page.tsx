"use client";

import React, { useState } from 'react';
import { useAuth } from '../../../components/providers/AuthProvider';
import { Globe, Plus, ArrowLeft } from 'lucide-react';
import MyDomainsSection from '../../../components/dashboard/domain/MyDomainsSection';
import DomainSearchStep from '../../../components/dashboard/domain/DomainSearchStep';
import DomainPaymentStep from '../../../components/dashboard/domain/DomainPaymentStep';
import DomainSiteMappingStep from '../../../components/dashboard/domain/DomainSiteMappingStep';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

type WizardStep = 'search' | 'payment' | 'mapping';

const DomainPage = () => {
  const { user, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState<'my-domains' | 'register'>('my-domains');
  const [wizardStep, setWizardStep] = useState<WizardStep>('search');
  const [selectedDomain, setSelectedDomain] = useState<DomainCheckResult | null>(null);
  const [registeredDomain, setRegisteredDomain] = useState<string | null>(null);

  // Handle domain selection from search step
  const handleDomainSelect = (domain: DomainCheckResult) => {
    setSelectedDomain(domain);
    setWizardStep('payment');
  };

  // Handle payment initiation
  const handlePaymentInitiated = () => {
    // Payment step will redirect to Stripe, so no additional action needed here
  };

  // Handle going back to search
  const handleBackToSearch = () => {
    setWizardStep('search');
    setSelectedDomain(null);
  };

  // Handle domain mapping completion
  const handleMappingComplete = async (_siteId: string) => {
    // This would be called after successful payment and domain registration
    // For now, just redirect to dashboard
    window.location.href = '/dashboard';
  };

  // Handle tab switching
  const handleTabSwitch = (tab: 'my-domains' | 'register') => {
    setActiveTab(tab);
    if (tab === 'register') {
      setWizardStep('search');
      setSelectedDomain(null);
      setRegisteredDomain(null);
    }
  };

  if (authLoading || !user) {
    return <div className="py-8 text-center">Checking authentication...</div>;
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="mb-2 text-2xl font-bold text-gray-800 sm:mb-4 sm:text-3xl lg:text-4xl">
            Domain Management
          </h1>
          <p className="text-sm text-gray-600 sm:text-base">
            Manage your domains and register new ones for your websites.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex -mb-px space-x-8">
              <button
                onClick={() => handleTabSwitch('my-domains')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'my-domains'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Globe className="inline w-5 h-5 mr-2" />
                My Domains
              </button>
              <button
                onClick={() => handleTabSwitch('register')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'register'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Plus className="inline w-5 h-5 mr-2" />
                Register New Domain
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {activeTab === 'my-domains' ? (
            <div className="p-6">
              <MyDomainsSection />
            </div>
          ) : (
            <div className="p-6">
              {/* Registration Wizard */}
              {wizardStep === 'search' && (
                <DomainSearchStep
                  onDomainSelect={handleDomainSelect}
                  selectedDomain={selectedDomain}
                />
              )}

              {wizardStep === 'payment' && selectedDomain && (
                <div className="space-y-4">
                  <button
                    onClick={handleBackToSearch}
                    className="flex items-center text-gray-600 transition-colors hover:text-gray-800"
                  >
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    Back to Search
                  </button>
                  <DomainPaymentStep
                    selectedDomain={selectedDomain}
                    onPaymentInitiated={handlePaymentInitiated}
                    onBack={handleBackToSearch}
                  />
                </div>
              )}

              {wizardStep === 'mapping' && registeredDomain && (
                <DomainSiteMappingStep
                  domainName={registeredDomain}
                  onComplete={handleMappingComplete}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DomainPage;