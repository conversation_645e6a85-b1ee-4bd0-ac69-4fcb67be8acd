//without loader
// 'use client';

// import LandingPage from '@/components/landing/LandingPage';
// import BuilderModal from '@/components/modals/BuilderModal'; 
// import { useSearchParams } from 'next/navigation';
// import { SiteBuilderProvider } from '@/context/SiteBuilderContext'; // ✅ import the provider

// export default function Home() {
//   const searchParams = useSearchParams();
//   const showBuilder = searchParams.get('builder') === 'true';

//   return (
//     <div className="relative">
//       <LandingPage />

//       {showBuilder && (
//         <SiteBuilderProvider>
//           <BuilderModal />
//         </SiteBuilderProvider>
//       )}
//     </div>
//   );
// }


//with loader
'use client';

import LandingPage from '@/components/landing/LandingPage';
import BuilderModal from '@/components/modals/BuilderModal'; 
import { useSearchParams } from 'next/navigation';
import { SiteBuilderProvider } from '@/context/SiteBuilderContext';
import GlobalLoader from '@/components/GlobalLoader'; // ✅ Add this

export default function Home() {
  const searchParams = useSearchParams();
  const showBuilder = searchParams.get('builder') === 'true';

  return (
    <div className="relative">
      <LandingPage />

      {showBuilder && (
        <SiteBuilderProvider>
          <GlobalLoader /> {/* ✅ Use it inside the provider */}
          <BuilderModal />
        </SiteBuilderProvider>
      )}
    </div>
  );
}
