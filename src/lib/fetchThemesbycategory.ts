// fetchThemesByCategory.ts
const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;

export interface WordPressThemeByCategory {
  site_id: string;
  site_path: string;
  templateId: number;
  name: string;
  description: string;
  thumbnailUrl: string;
  htmlPath: string;
  placeholders: {
    websiteName: string;
    heroImage: string;
  };
}

export async function fetchThemesByCategory(category: string): Promise<WordPressThemeByCategory[]> {
  try {
    const res = await fetch(`${BASE_URL}/wp-json/custom/v1/templates-by-category?category=${encodeURIComponent(category)}`);
    if (!res.ok) throw new Error(`Error: ${res.status}`);
    return await res.json();
  } catch (err) {
    console.error('Failed to fetch category-based themes:', err);
    return [];
  }
}
