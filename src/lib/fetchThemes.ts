const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;

export interface WordPressTheme {
  slug: string;
  name: string;
  description: string;
  screenshot: string;
  version: string;
  author: string;
}

export async function fetchThemes(): Promise<WordPressTheme[]> {
  try {
    const res = await fetch(`${BASE_URL}/wp-json/custom/v1/themes`); 
    if (!res.ok) throw new Error(`Error: ${res.status}`);
    const data = await res.json();
    return data;
  } catch (err) {
    console.error('Failed to fetch themes:', err);
    return [];
  }
}

