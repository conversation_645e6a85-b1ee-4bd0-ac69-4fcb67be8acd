// // src/lib/utils.ts

// export const handlePreviewClick = (
//   themeSlug: string,
//   siteName: string,
//   brief: string,
//   heroImage: string,
//   headerImage: string
// ) => {
//   const form = document.createElement('form');
//   form.method = 'POST';
//   form.action = 'http://localhost/';
//   form.target = '_blank';

//   const themeInput = document.createElement('input');
//   themeInput.type = 'hidden';
//   themeInput.name = 'preview_theme';
//   themeInput.value = themeSlug;
//   form.appendChild(themeInput);

//   const dataInput = document.createElement('input');
//   dataInput.type = 'hidden';
//   dataInput.name = 'preview_data';
//   dataInput.value = JSON.stringify({
//     site_name: siteName,
//     description: brief,
//     hero_image: heroImage,
//     header_image: headerImage
//   });
//   form.appendChild(dataInput);

//   document.body.appendChild(form);
//   form.submit();
//   document.body.removeChild(form);
// };

const BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API;

export const handlePreviewClick = (
  themeSlug: string,
  siteName: string,
  brief: string,
  heroImage: string,
  headerImage: string
) => {
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = `${BASE_URL}`; // Adjust the endpoint as needed
  form.target = '_blank';

  const themeInput = document.createElement('input');
  themeInput.type = 'hidden';
  themeInput.name = 'preview_theme';
  themeInput.value = themeSlug;
  form.appendChild(themeInput);

  const dataInput = document.createElement('input');
  dataInput.type = 'hidden';
  dataInput.name = 'preview_data';
  dataInput.value = JSON.stringify({
    site_name: siteName,
    description: brief,
    hero_image: heroImage,
    header_image: headerImage
  });
  form.appendChild(dataInput);

  document.body.appendChild(form);
  form.submit();
  document.body.removeChild(form);
};

