export function buildFinalReviewUrl({
  sitePath,
  previewKey,
  heroImageUrl,
  headerImageUrl,
  primaryColor,
  headingFont,
  bodyFont,
}: {
  sitePath: string;
  previewKey: string;
  heroImageUrl: string;
  headerImageUrl: string;
  primaryColor: string;
  headingFont: string;
  bodyFont: string;
}) {
  const url = new URL(sitePath);
  url.searchParams.set('preview_key', previewKey);
  if (heroImageUrl) url.searchParams.set('heroImageUrl', heroImageUrl);
  if (headerImageUrl) url.searchParams.set('headerImageUrl', headerImageUrl);
  url.searchParams.set('primaryColor', primaryColor);
  url.searchParams.set('headingFont', headingFont);
  url.searchParams.set('bodyFont', bodyFont);
  url.searchParams.set('_refresh', Date.now().toString()); // cache bust
  return url.toString();
}
