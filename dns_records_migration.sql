-- Create DNS records table
CREATE TABLE IF NOT EXISTS dns_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    domain_id UUID NOT NULL REFERENCES domains(id) ON DELETE CASCADE,
    type VARCHAR(10) NOT NULL CHECK (type IN ('A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SRV', 'CAA')),
    name VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    ttl INTEGER NOT NULL DEFAULT 3600 CHECK (ttl >= 60 AND ttl <= 604800),
    priority INTEGER CHECK (priority >= 0 AND priority <= 65535),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('active', 'pending', 'error')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dns_records_domain_id ON dns_records(domain_id);
CREATE INDEX IF NOT EXISTS idx_dns_records_type ON dns_records(type);
CREATE INDEX IF NOT EXISTS idx_dns_records_status ON dns_records(status);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_dns_records_updated_at 
    BEFORE UPDATE ON dns_records 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS (Row Level Security) policies
ALTER TABLE dns_records ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to see only their own DNS records
CREATE POLICY "Users can view their own DNS records" ON dns_records
    FOR SELECT USING (
        domain_id IN (
            SELECT id FROM domains WHERE user_id = auth.uid()
        )
    );

-- Policy to allow users to insert DNS records for their own domains
CREATE POLICY "Users can insert DNS records for their own domains" ON dns_records
    FOR INSERT WITH CHECK (
        domain_id IN (
            SELECT id FROM domains WHERE user_id = auth.uid()
        )
    );

-- Policy to allow users to update DNS records for their own domains
CREATE POLICY "Users can update DNS records for their own domains" ON dns_records
    FOR UPDATE USING (
        domain_id IN (
            SELECT id FROM domains WHERE user_id = auth.uid()
        )
    );

-- Policy to allow users to delete DNS records for their own domains
CREATE POLICY "Users can delete DNS records for their own domains" ON dns_records
    FOR DELETE USING (
        domain_id IN (
            SELECT id FROM domains WHERE user_id = auth.uid()
        )
    ); 